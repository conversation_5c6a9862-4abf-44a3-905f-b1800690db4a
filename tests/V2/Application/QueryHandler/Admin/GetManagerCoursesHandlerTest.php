<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Admin;

use App\Entity\User;
use App\Tests\V2\Mother\Course\Manager\CourseManagerMother;
use App\V2\Application\Query\Admin\GetManagerCourses;
use App\V2\Application\QueryHandler\Admin\GetManagerCoursesHandler;
use App\V2\Domain\Course\Manager\CourseManagerCollection;
use App\V2\Domain\Course\Manager\CourseManagerCriteria;
use App\V2\Domain\Course\Manager\CourseManagerRepository;
use App\V2\Domain\Course\Manager\Exceptions\GetManagerCoursesException;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class GetManagerCoursesHandlerTest extends TestCase
{
    private UserRepository|MockObject $userRepository;
    private CourseManagerRepository|MockObject $courseManagerRepository;
    private GetManagerCoursesHandler $handler;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->userRepository = $this->createMock(UserRepository::class);
        $this->courseManagerRepository = $this->createMock(CourseManagerRepository::class);

        $this->handler = new GetManagerCoursesHandler(
            $this->userRepository,
            $this->courseManagerRepository
        );
    }

    /**
     * @throws InfrastructureException
     * @throws GetManagerCoursesException
     * @throws Exception
     * @throws CriteriaException
     * @throws CollectionException
     * @throws UserNotFoundException
     */
    public function testHandleSuccessWithManagerUser(): void
    {
        $userId = new Id(123);
        $query = new GetManagerCourses($userId);

        $user = $this->createMock(User::class);
        $user->method('isManager')->willReturn(true);

        $courseManager1 = CourseManagerMother::create(
            userId: new Id(123),
            courseId: new Id(1)
        );
        $courseManager2 = CourseManagerMother::create(
            userId: new Id(123),
            courseId: new Id(2)
        );

        $expectedCollection = new CourseManagerCollection([$courseManager1, $courseManager2]);

        $this->userRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (UserCriteria $criteria) use ($userId) {
                return $criteria->getId() === $userId;
            }))
            ->willReturn($user);

        $this->courseManagerRepository
            ->expects($this->once())
            ->method('findBy')
            ->with($this->callback(function (CourseManagerCriteria $criteria) use ($userId) {
                return $criteria->getUserId() === $userId;
            }))
            ->willReturn($expectedCollection);

        $result = $this->handler->handle($query);

        $this->assertEquals($expectedCollection, $result);
        $this->assertCount(2, $result);
    }

    /**
     * @throws InfrastructureException
     * @throws GetManagerCoursesException
     * @throws CriteriaException
     */
    public function testHandleThrowsUserNotFoundExceptionWhenUserDoesNotExist(): void
    {
        $userId = new Id(999);
        $query = new GetManagerCourses($userId);

        $this->userRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new UserNotFoundException());

        $this->courseManagerRepository
            ->expects($this->never())
            ->method('findBy');

        $this->expectException(UserNotFoundException::class);

        $this->handler->handle($query);
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws UserNotFoundException
     * @throws CriteriaException
     */
    public function testHandleThrowsExceptionWhenUserIsNotManager(): void
    {
        $userId = new Id(123);
        $query = new GetManagerCourses($userId);

        $user = $this->createMock(User::class);
        $user->method('isManager')->willReturn(false);

        $this->userRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($user);

        $this->courseManagerRepository
            ->expects($this->never())
            ->method('findBy');

        $this->expectException(GetManagerCoursesException::class);
        $this->expectExceptionMessage('User is not a manager');

        $this->handler->handle($query);
    }

    /**
     * @throws InfrastructureException
     * @throws GetManagerCoursesException
     * @throws Exception
     * @throws UserNotFoundException
     * @throws CriteriaException
     * @throws CollectionException
     */
    public function testHandleReturnsEmptyCollectionWhenManagerHasNoCourses(): void
    {
        $userId = new Id(123);
        $query = new GetManagerCourses($userId);

        $user = $this->createMock(User::class);
        $user->method('isManager')->willReturn(true);

        $emptyCollection = new CourseManagerCollection([]);

        $this->userRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($user);

        $this->courseManagerRepository
            ->expects($this->once())
            ->method('findBy')
            ->willReturn($emptyCollection);

        $result = $this->handler->handle($query);

        $this->assertEquals($emptyCollection, $result);
        $this->assertCount(0, $result);
    }

    /**
     * @throws GetManagerCoursesException
     * @throws Exception
     * @throws UserNotFoundException
     * @throws CriteriaException
     */
    public function testHandleThrowsInfrastructureExceptionWhenRepositoryFails(): void
    {
        $userId = new Id(123);
        $query = new GetManagerCourses($userId);

        $user = $this->createMock(User::class);
        $user->method('isManager')->willReturn(true);

        $this->userRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($user);

        $this->courseManagerRepository
            ->expects($this->once())
            ->method('findBy')
            ->willThrowException(new InfrastructureException('Database error'));

        $this->expectException(InfrastructureException::class);
        $this->expectExceptionMessage('Database error');

        $this->handler->handle($query);
    }
}
