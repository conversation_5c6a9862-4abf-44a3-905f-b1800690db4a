<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler;

use App\V2\Application\Query\HealthCheck;
use App\V2\Application\QueryHandler\HealthCheckHandler;
use App\V2\Domain\Shared\Email\Email;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class HealthCheckHandlerTest extends TestCase
{
    private UserRepository $userRepository;
    private HealthCheckHandler $handler;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->userRepository = $this->createMock(UserRepository::class);
        $this->handler = new HealthCheckHandler($this->userRepository);
    }

    public function testHandleWithUser(): void
    {
        $query = new HealthCheck();

        $this->userRepository
            ->expects($this->once())
            ->method('countBy')
            ->willReturn(1);

        $this->handler->handle($query);
    }

    public function testHandleWithUserNotFound(): void
    {
        $query = new HealthCheck();

        $this->userRepository
            ->expects($this->once())
            ->method('countBy')
            ->willReturn(0);

        $this->handler->handle($query);
    }

    public function testHandleWithException(): void
    {
        $query = new HealthCheck();

        $this->userRepository
            ->expects($this->once())
            ->method('countBy')
            ->willThrowException(new \Exception('Database connection failed'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database connection failed');

        $this->handler->handle($query);
    }
}
