<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Service\Course;

use App\Entity\User;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\Course\Creator\CourseCreatorMother;
use App\V2\Application\Service\Course\CourseAuthorizationService;
use App\V2\Domain\Course\Creator\CourseCreatorNotFoundException;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Domain\Course\Exceptions\UserNotAuthorizedException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use PHPUnit\Framework\TestCase;

class CourseAuthorizationServiceTest extends TestCase
{
    private function getService(
        ?CourseCreatorRepository $courseCreatorRepository = null,
    ): CourseAuthorizationService {
        return new CourseAuthorizationService(
            courseCreatorRepository: $courseCreatorRepository ?? $this->createMock(CourseCreatorRepository::class),
        );
    }

    /**
     * @throws UserNotAuthorizedException
     * @throws InfrastructureException
     */
    public function testAsAdmin(): void
    {
        $this->expectNotToPerformAssertions();
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_ADMIN]);
        $course = CourseMother::create(id: 1);
        $service = $this->getService();
        $service->ensureUserCanManageCourseContent($user, $course);
    }

    /**
     * @throws UserNotAuthorizedException
     * @throws InfrastructureException
     */
    public function testAsTheCourseCreator(): void
    {
        $this->expectNotToPerformAssertions();
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_CREATOR]);
        $course = CourseMother::create(id: 1, createdBy: $user);
        $service = $this->getService();
        $service->ensureUserCanManageCourseContent($user, $course);
    }

    /**
     * @throws UserNotAuthorizedException
     * @throws InfrastructureException
     */
    public function testAsManager(): void
    {
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_MANAGER]);
        $course = CourseMother::create(id: 1);
        $service = $this->getService();
        $this->expectExceptionObject(
            UserNotAuthorizedException::userNotAllowedToManageCourseContent($user->getEmail())
        );
        $service->ensureUserCanManageCourseContent($user, $course);
    }

    /**
     * @throws UserNotAuthorizedException
     * @throws InfrastructureException
     */
    public function testAsOtherCreator(): void
    {
        $course = CourseMother::create(
            id: 1,
            createdBy: UserMother::create(id: 1, email: '<EMAIL>', roles: [User::ROLE_CREATOR])
        );
        $courseCreatorRepository = $this->createMock(CourseCreatorRepository::class);

        $numberOfCalls = $this->exactly(2);
        $courseCreatorRepository->expects($numberOfCalls)
            ->method('findOneBy')
            ->willReturnCallback(function () use ($numberOfCalls) {
                if (1 === $numberOfCalls->numberOfInvocations()) {
                    return CourseCreatorMother::create(
                        userId: new Id(1),
                        courseId: new Id(1)
                    );
                }

                throw new CourseCreatorNotFoundException();
            });
        $service = $this->getService(courseCreatorRepository: $courseCreatorRepository);
        $service->ensureUserCanManageCourseContent(
            user: UserMother::create(id: 2, email: '<EMAIL>', roles: [User::ROLE_CREATOR]),
            course: $course
        );

        $this->expectExceptionObject(
            UserNotAuthorizedException::userNotAllowedToManageCourseContent('<EMAIL>')
        );
        $service->ensureUserCanManageCourseContent(
            user: UserMother::create(id: 3, email: '<EMAIL>', roles: [User::ROLE_CREATOR]),
            course: $course
        );
    }
}
