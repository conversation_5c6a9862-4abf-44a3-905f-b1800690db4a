<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\DTO\User;

use App\Entity\User;
use App\Tests\Mother\Entity\UserMother;
use App\V2\Application\DTO\User\UserListItemDTO;
use App\V2\Application\DTO\User\UserListItemDTOCollection;
use App\V2\Application\DTO\User\UserListItemDTOFactory;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\User\UserCollection;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class UserListItemDTOFactoryTest extends TestCase
{
    /**
     * Test that the UserListItemDTOFactory correctly builds a DTO from a User entity,
     * assigning the correct permission flags (`editable`, `deletable`, `allowLoginAs`).
     */
    #[DataProvider('providerCreateFromUserWithRoles')]
    public function testCreateFromUserWithRoles(
        User $user,
        User $requestUser,
        UserListItemDTO $expectedUserListItemDTO
    ): void {
        $this->assertEquals(
            $expectedUserListItemDTO,
            UserListItemDTOFactory::createFromUser($user, $requestUser)
        );
    }

    public static function providerCreateFromUserWithRoles(): \Generator
    {
        yield 'Admin user has full permissions on any user' => [
            'user' => UserMother::create(
                id: 10,
                firstName: 'John',
                lastName: 'Doe',
                email: '<EMAIL>',
                roles: ['ROLE_USER']
            ),
            'requestUser' => UserMother::create(
                id: 999,
                firstName: 'Request',
                lastName: 'Admin',
                email: '<EMAIL>',
                roles: ['ROLE_ADMIN']
            ),
            'expectedUserListItemDTO' => new UserListItemDTO(
                id: 10,
                avatar: null,
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                roles: ['ROLE_USER'],
                isActive: true,
                points: null,
                editable: true,
                deletable: true,
                allowLoginAs: true
            ),
        ];

        yield 'Manager creator with edit and delete permissions' => [
            'user' => UserMother::create(
                id: 20,
                firstName: 'Jane',
                lastName: 'Smith',
                email: '<EMAIL>',
                roles: ['ROLE_USER'],
                createdBy: UserMother::create(
                    id: 998,
                    firstName: 'Request',
                    lastName: 'Manager',
                    email: '<EMAIL>',
                    roles: ['ROLE_MANAGER'],
                ),
            ),
            'requestUser' => UserMother::create(
                id: 998,
                firstName: 'Request',
                lastName: 'Manager',
                email: '<EMAIL>',
                roles: ['ROLE_MANAGER'],
            ),
            'expectedUserListItemDTO' => new UserListItemDTO(
                id: 20,
                avatar: null,
                email: '<EMAIL>',
                firstName: 'Jane',
                lastName: 'Smith',
                roles: ['ROLE_USER'],
                isActive: true,
                points: null,
                editable: true,
                deletable: true,
                allowLoginAs: false
            ),
        ];

        yield 'Manager non-creator cant impersonate, edit and delete' => [
            'user' => UserMother::create(
                id: 30,
                firstName: 'Sam',
                lastName: 'Taylor',
                email: '<EMAIL>',
                roles: ['ROLE_USER']
            ),
            'requestUser' => UserMother::create(
                id: 997,
                firstName: 'Request',
                lastName: 'Manager',
                email: '<EMAIL>',
                roles: ['ROLE_MANAGER']
            ),
            'expectedUserListItemDTO' => new UserListItemDTO(
                id: 30,
                avatar: null,
                email: '<EMAIL>',
                firstName: 'Sam',
                lastName: 'Taylor',
                roles: ['ROLE_USER'],
                isActive: true,
                points: null,
                editable: false,
                deletable: false,
                allowLoginAs: false
            ),
        ];

        yield 'Support can impersonate but not edit or delete' => [
            'user' => UserMother::create(
                id: 31,
                firstName: 'Sam',
                lastName: 'Gamyi',
                email: '<EMAIL>',
                roles: ['ROLE_USER']
            ),
            'requestUser' => UserMother::create(
                id: 997,
                firstName: 'Request',
                lastName: 'Support',
                email: '<EMAIL>',
                roles: ['ROLE_SUPPORT']
            ),
            'expectedUserListItemDTO' => new UserListItemDTO(
                id: 31,
                avatar: null,
                email: '<EMAIL>',
                firstName: 'Sam',
                lastName: 'Gamyi',
                roles: ['ROLE_USER'],
                isActive: true,
                points: null,
                editable: false,
                deletable: false,
                allowLoginAs: true
            ),
        ];

        yield 'User with no special permissions' => [
            'user' => UserMother::create(
                id: 40,
                firstName: 'Alice',
                lastName: 'Jones',
                email: '<EMAIL>',
                roles: ['ROLE_USER']
            ),
            'requestUser' => UserMother::create(
                id: 996,
                firstName: 'Request',
                lastName: 'User',
                email: '<EMAIL>',
                roles: ['ROLE_USER']
            ),
            'expectedUserListItemDTO' => new UserListItemDTO(
                id: 40,
                avatar: null,
                email: '<EMAIL>',
                firstName: 'Alice',
                lastName: 'Jones',
                roles: ['ROLE_USER'],
                isActive: true,
                points: null,
                editable: false,
                deletable: false,
                allowLoginAs: false
            ),
        ];
    }

    /**
     * Test that creating a DTO collection from a UserCollection with users.
     *
     * @throws Exception
     * @throws CollectionException
     */
    public function testCreateCollectionFromUserCollectionWithUsers()
    {
        $usersData = [
            [
                'id' => 1,
                'email' => '<EMAIL>',
                'avatar' => null,
                'firstName' => 'User',
                'lastName' => 'One',
                'roles' => ['ROLE_USER'],
                'isActive' => true,
                'points' => null,
            ],
            [
                'id' => 2,
                'email' => '<EMAIL>',
                'avatar' => null,
                'firstName' => 'User',
                'lastName' => 'Two',
                'roles' => ['ROLE_MANAGER'],
                'isActive' => false,
                'points' => null,
            ],
        ];

        $users = [];
        foreach ($usersData as $data) {
            $users[] = UserMother::create(
                id: $data['id'],
                firstName: $data['firstName'],
                lastName: $data['lastName'],
                email: $data['email'],
                isActive: $data['isActive'],
                roles: $data['roles'],
            );
        }

        $userCollection = new UserCollection($users);

        $requestUser = UserMother::create(roles: ['ROLE_ADMIN']);

        $userListItems = UserListItemDTOFactory::createCollectionFromUserCollection($userCollection, $requestUser);

        $this->assertInstanceOf(UserListItemDTOCollection::class, $userListItems);
        $this->assertCount(\count($usersData), $userListItems);

        foreach ($userListItems as $index => $userListItem) {
            $expected = $usersData[$index];

            $this->assertEquals($expected['id'], $userListItem->getId());
            $this->assertEquals($expected['email'], $userListItem->getEmail());
            $this->assertEquals($expected['avatar'], $userListItem->getAvatar());
            $this->assertEquals($expected['firstName'], $userListItem->getFirstName());
            $this->assertEquals($expected['lastName'], $userListItem->getLastName());
            $this->assertEquals($expected['isActive'], $userListItem->isActive());
            $this->assertEquals($expected['points'], $userListItem->getPoints());
        }
    }

    /**
     * Test that creating a DTO collection from an empty UserCollection
     * returns an empty UserListItemDTOCollection instance.
     *
     * This ensures the factory handles empty inputs gracefully without errors,
     * returning a collection with zero items.
     *
     * @throws CollectionException
     * @throws Exception
     */
    public function testCreateCollectionFromEmptyUserCollection()
    {
        $userCollection = new UserCollection([]);
        $requestUser = UserMother::create(roles: ['ROLE_ADMIN']);
        $userListItems = UserListItemDTOFactory::createCollectionFromUserCollection($userCollection, $requestUser);

        $this->assertInstanceOf(UserListItemDTOCollection::class, $userListItems);
        $this->assertCount(0, $userListItems);
    }
}
