<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Entity\Course;
use App\Entity\User;
use App\Tests\V2\Mother\Course\Creator\CourseCreatorMother;
use App\V2\Application\Command\DeleteCourseCreatorCommand;
use App\V2\Application\CommandHandler\DeleteCourseCreatorCommandHandler;
use App\V2\Domain\Course\CourseRepository;
use App\V2\Domain\Course\Creator\CourseCreatorNotFoundException;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Domain\Course\Creator\Exceptions\CreatorNotAuthorizedException;
use App\V2\Domain\Course\Exceptions\CourseNotFoundException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class DeleteCourseCreatorCommandHandlerTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHandler(
        ?UserRepository $userRepository = null,
        ?CourseCreatorRepository $courseCreatorRepository = null,
        ?CourseRepository $courseRepository = null,
    ): DeleteCourseCreatorCommandHandler {
        return new DeleteCourseCreatorCommandHandler(
            userRepository: $userRepository ?? $this->createMock(UserRepository::class),
            courseCreatorRepository: $courseCreatorRepository ?? $this->createMock(CourseCreatorRepository::class),
            courseRepository: $courseRepository ?? $this->createMock(CourseRepository::class),
        );
    }

    /**
     * @throws CourseCreatorNotFoundException
     * @throws CourseNotFoundException
     * @throws CreatorNotAuthorizedException
     * @throws CriteriaException
     * @throws Exception
     * @throws InfrastructureException
     * @throws UserNotFoundException
     */
    #[DataProvider('adminRolesProvider')]
    public function testHandleSuccessAsAdmin($roles): void
    {
        $courseId = new Id(1);
        $userId = new Id(2);

        $requestUser = $this->createMock(User::class);
        $requestUser->method('getRoles')->willReturn($roles);

        $userToRemove = $this->createMock(User::class);
        $userToRemove->method('getRoles')->willReturn([User::ROLE_CREATOR]);
        $userToRemove->method('getId')->willReturn($userId->value());

        $course = $this->createMock(Course::class);
        $course->method('getId')->willReturn($courseId->value());
        $course->method('getCreatedBy')->willReturn(null); // Admin doesn't need to be the owner

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($userToRemove);

        $courseRepository = $this->createMock(CourseRepository::class);
        $courseRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($course);

        $courseCreatorRepository = $this->createMock(CourseCreatorRepository::class);
        $courseCreatorRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn(CourseCreatorMother::create(userId: $userId, courseId: $courseId));

        $courseCreatorRepository->expects($this->once())
            ->method('delete');

        $handler = $this->getHandler(
            userRepository: $userRepository,
            courseCreatorRepository: $courseCreatorRepository,
            courseRepository: $courseRepository,
        );

        $command = new DeleteCourseCreatorCommand(courseId: $courseId, userId: $userId, requestUser: $requestUser);
        $handler->handle($command);
    }

    public static function adminRolesProvider(): \Generator
    {
        yield 'Admin' => [
            'roles' => [User::ROLE_ADMIN],
        ];

        yield 'Admin with creator role' => [
            'roles' => [User::ROLE_ADMIN, User::ROLE_CREATOR],
        ];
    }

    /**
     * @throws CourseCreatorNotFoundException
     * @throws CourseNotFoundException
     * @throws CreatorNotAuthorizedException
     * @throws CriteriaException
     * @throws Exception
     * @throws InfrastructureException
     * @throws UserNotFoundException
     */
    public function testHandleSuccessAsCourseCreator(): void
    {
        $courseId = new Id(1);
        $userId = new Id(2);

        $requestUser = $this->createMock(User::class);
        $requestUser->method('getRoles')->willReturn([User::ROLE_CREATOR]);
        $requestUser->method('getId')->willReturn(3);

        $userToRemove = $this->createMock(User::class);
        $userToRemove->method('getRoles')->willReturn([User::ROLE_CREATOR]);
        $userToRemove->method('getId')->willReturn($userId->value());

        $course = $this->createMock(Course::class);
        $course->method('getId')->willReturn($courseId->value());
        $course->method('getCreatedBy')->willReturn($requestUser); // Course is owned by the request user

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($userToRemove);

        $courseRepository = $this->createMock(CourseRepository::class);
        $courseRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($course);

        $courseCreatorRepository = $this->createMock(CourseCreatorRepository::class);
        $courseCreatorRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn(CourseCreatorMother::create(userId: $userId, courseId: $courseId));

        $courseCreatorRepository->expects($this->once())
            ->method('delete');

        $handler = $this->getHandler(
            userRepository: $userRepository,
            courseCreatorRepository: $courseCreatorRepository,
            courseRepository: $courseRepository,
        );

        $command = new DeleteCourseCreatorCommand(courseId: $courseId, userId: $userId, requestUser: $requestUser);
        $handler->handle($command);
    }

    /**
     * @throws CourseCreatorNotFoundException
     * @throws CourseNotFoundException
     * @throws CriteriaException
     * @throws Exception
     * @throws InfrastructureException
     * @throws CreatorNotAuthorizedException
     * @throws UserNotFoundException
     */
    public function testHandleThrowsCourseNotFound(): void
    {
        $courseId = new Id(1);
        $userId = new Id(2);

        $requestUser = $this->createMock(User::class);
        $requestUser->method('getRoles')->willReturn([User::ROLE_CREATOR]);

        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn($userId->value());

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($user);

        $courseRepository = $this->createMock(CourseRepository::class);
        $courseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new CourseNotFoundException());

        $handler = $this->getHandler(
            userRepository: $userRepository,
            courseRepository: $courseRepository
        );

        $command = new DeleteCourseCreatorCommand(courseId: $courseId, userId: $userId, requestUser: $requestUser);

        $this->expectException(CourseNotFoundException::class);
        $handler->handle($command);
    }

    /**
     * @throws CourseCreatorNotFoundException
     * @throws CourseNotFoundException
     * @throws CreatorNotAuthorizedException
     * @throws CriteriaException
     * @throws Exception
     * @throws InfrastructureException
     * @throws UserNotFoundException
     */
    public function testHandleThrowsCourseCreatorNotFound(): void
    {
        $courseId = new Id(1);
        $userId = new Id(2);

        $requestUser = $this->createMock(User::class);
        $requestUser->method('getRoles')->willReturn([User::ROLE_CREATOR]);
        $requestUser->method('getId')->willReturn(3);

        $course = $this->createMock(Course::class);
        $course->method('getId')->willReturn($courseId->value());
        $course->method('getCreatedBy')->willReturn($requestUser); // Course is owned by the request user

        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn($userId->value());

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($user);

        $courseRepository = $this->createMock(CourseRepository::class);
        $courseRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($course);

        $courseCreatorRepository = $this->createMock(CourseCreatorRepository::class);
        $courseCreatorRepository->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new CourseCreatorNotFoundException());

        $handler = $this->getHandler(
            userRepository: $userRepository,
            courseCreatorRepository: $courseCreatorRepository,
            courseRepository: $courseRepository,
        );

        $command = new DeleteCourseCreatorCommand(courseId: $courseId, userId: $userId, requestUser: $requestUser);

        $this->expectException(CourseCreatorNotFoundException::class);
        $handler->handle($command);
    }

    /**
     * @throws CourseCreatorNotFoundException
     * @throws CourseNotFoundException
     * @throws CreatorNotAuthorizedException
     * @throws CriteriaException
     * @throws Exception
     * @throws InfrastructureException
     * @throws UserNotFoundException
     */
    public function testHandleThrowsNotCourseOwner(): void
    {
        $courseId = new Id(1);
        $userId = new Id(2);
        $creatorUserId = 3;
        $otherUserId = 4;

        // Create a course owned by user 3
        $courseOwner = $this->createMock(User::class);
        $courseOwner->method('getId')->willReturn($creatorUserId);

        $course = $this->createMock(Course::class);
        $course->method('getId')->willReturn($courseId->value());
        $course->method('getCreatedBy')->willReturn($courseOwner);

        // Request user is user 4 (not the owner)
        $requestUser = $this->createMock(User::class);
        $requestUser->method('getRoles')->willReturn([User::ROLE_CREATOR]);
        $requestUser->method('getId')->willReturn($otherUserId);

        $userToRemove = $this->createMock(User::class);
        $userToRemove->method('getId')->willReturn($userId->value());

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($userToRemove);

        $courseRepository = $this->createMock(CourseRepository::class);
        $courseRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($course);

        $handler = $this->getHandler(
            userRepository: $userRepository,
            courseRepository: $courseRepository,
        );

        $command = new DeleteCourseCreatorCommand(courseId: $courseId, userId: $userId, requestUser: $requestUser);

        $this->expectException(CreatorNotAuthorizedException::class);
        $handler->handle($command);
    }
}
