<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Query\Admin;

use App\V2\Application\Query\Admin\GetManagerCourses;
use App\V2\Domain\Shared\Id\Id;
use PHPUnit\Framework\TestCase;

class GetManagerCoursesTest extends TestCase
{
    public function testGetManagerCoursesCreation(): void
    {
        $userId = new Id(123);

        $query = new GetManagerCourses($userId);

        $this->assertEquals($userId, $query->getUserId());
    }

    public function testGetManagerCoursesWithDifferentUserId(): void
    {
        $userId = new Id(456);

        $query = new GetManagerCourses($userId);

        $this->assertEquals($userId, $query->getUserId());
        $this->assertEquals(456, $query->getUserId()->value());
    }
}
