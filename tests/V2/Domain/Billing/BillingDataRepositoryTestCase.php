<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Billing;

use App\Tests\V2\Mother\Billing\BillingDataMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Billing\BillingDataCollection;
use App\V2\Domain\Billing\BillingDataCriteria;
use App\V2\Domain\Billing\BillingDataRepository;
use App\V2\Domain\Billing\Exception\BillingDataNotFoundException;
use App\V2\Domain\Billing\Exception\BillingDataRepositoryException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use PHPUnit\Framework\TestCase;

abstract class BillingDataRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): BillingDataRepository;

    /**
     * @throws BillingDataRepositoryException
     * @throws BillingDataNotFoundException
     * @throws CriteriaException
     * @throws \Exception
     */
    public function testPutAndFindById(): void
    {
        $billingData = BillingDataMother::create();
        $repository = $this->getRepository();

        $repository->put($billingData);

        $found = $repository->findOneBy(
            BillingDataCriteria::createById($billingData->getId())
        );

        $this->assertEquals($billingData, $found);
    }

    /**
     * @throws BillingDataRepositoryException
     * @throws BillingDataNotFoundException
     * @throws \Exception
     */
    public function testFindByUserId(): void
    {
        $userId = IdMother::create();
        $billingData = BillingDataMother::create(userId: $userId);
        $repository = $this->getRepository();

        $repository->put($billingData);

        $found = $repository->findOneBy(
            BillingDataCriteria::createEmpty()->filterByUserId($userId)
        );

        $this->assertEquals($billingData, $found);
    }

    /**
     * @throws BillingDataRepositoryException
     * @throws BillingDataNotFoundException
     */
    public function testFindByTin(): void
    {
        $tin = '87654321B';
        $billingData = BillingDataMother::create(tin: $tin);
        $repository = $this->getRepository();

        $repository->put($billingData);

        $found = $repository->findOneBy(
            BillingDataCriteria::createEmpty()->filterByTin($tin)
        );

        $this->assertEquals($billingData, $found);
    }

    /**
     * @throws BillingDataRepositoryException
     */
    public function testFindAll(): void
    {
        $billingData1 = BillingDataMother::create();
        $billingData2 = BillingDataMother::create();
        $repository = $this->getRepository();

        $repository->put($billingData1);
        $repository->put($billingData2);

        $found = $repository->findBy(BillingDataCriteria::createEmpty());

        $this->assertCount(2, $found);
        $this->assertInstanceOf(BillingDataCollection::class, $found);
    }

    /**
     * @throws BillingDataRepositoryException
     * @throws BillingDataNotFoundException
     * @throws CriteriaException
     */
    public function testDelete(): void
    {
        $billingData = BillingDataMother::create();
        $repository = $this->getRepository();

        $repository->put($billingData);
        $repository->delete($billingData);

        $this->expectException(BillingDataNotFoundException::class);
        $repository->findOneBy(BillingDataCriteria::createById($billingData->getId()));
    }

    /**
     * @throws BillingDataRepositoryException
     * @throws CriteriaException
     * @throws \Exception
     */
    public function testFindByIdNotFound(): void
    {
        $repository = $this->getRepository();

        $this->expectException(BillingDataNotFoundException::class);
        $repository->findOneBy(BillingDataCriteria::createById(UuidMother::create()));
    }

    /**
     * @throws BillingDataRepositoryException
     * @throws \Exception
     */
    public function testFindByUserIdNotFound(): void
    {
        $repository = $this->getRepository();

        $this->expectException(BillingDataNotFoundException::class);
        $repository->findOneBy(
            BillingDataCriteria::createEmpty()->filterByUserId(IdMother::create())
        );
    }

    /**
     * @throws BillingDataRepositoryException
     */
    public function testFindByTinNotFound(): void
    {
        $repository = $this->getRepository();

        $this->expectException(BillingDataNotFoundException::class);
        $repository->findOneBy(
            BillingDataCriteria::createEmpty()->filterByTin('NONEXISTENT')
        );
    }

    /**
     * @throws BillingDataRepositoryException
     * @throws BillingDataNotFoundException
     * @throws CriteriaException
     */
    public function testUpdateBillingData(): void
    {
        $billingData = BillingDataMother::create(firstName: 'Original');
        $repository = $this->getRepository();

        $repository->put($billingData);

        $updatedBillingData = BillingDataMother::create(
            id: $billingData->getId(),
            userId: $billingData->getUserId(),
            firstName: 'Updated'
        );

        $repository->put($updatedBillingData);

        $found = $repository->findOneBy(
            BillingDataCriteria::createById($billingData->getId())
        );

        $this->assertEquals('Updated', $found->getFirstName());
    }

    /**
     * @throws BillingDataRepositoryException
     * @throws \Exception
     */
    public function testFindByMultipleCriteria(): void
    {
        $userId = IdMother::create();
        $tin = '11111111A';
        $billingData1 = BillingDataMother::create(userId: $userId, tin: $tin);
        $billingData2 = BillingDataMother::create(userId: $userId, tin: '22222222B');
        $billingData3 = BillingDataMother::create(userId: IdMother::create(), tin: $tin);
        $repository = $this->getRepository();

        $repository->put($billingData1);
        $repository->put($billingData2);
        $repository->put($billingData3);

        $found = $repository->findBy(
            BillingDataCriteria::createEmpty()
                ->filterByUserId($userId)
                ->filterByTin($tin)
        );

        $this->assertCount(1, $found);
        $this->assertEquals($billingData1, $found->first());
    }
}
