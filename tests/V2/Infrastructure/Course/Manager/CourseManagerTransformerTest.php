<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Course\Manager;

use App\Tests\V2\Mother\Course\Manager\CourseManagerMother;
use App\V2\Domain\Course\Manager\CourseManagerCollection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Course\Manager\CourseManagerTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class CourseManagerTransformerTest extends TestCase
{
    /**
     * @throws CollectionException
     */
    #[DataProvider('courseManagerCollectionProvider')]
    public function testFromCollectionToArray(array $courseManagerData, array $expectedResult, int $expectedCount): void
    {
        $courseManagers = [];
        foreach ($courseManagerData as $data) {
            $courseManagers[] = CourseManagerMother::create(
                userId: new Id($data['userId']),
                courseId: new Id($data['courseId'])
            );
        }

        $collection = new CourseManagerCollection($courseManagers);
        $result = CourseManagerTransformer::fromCollectionToArray($collection);

        $this->assertEquals($expectedResult, $result);
        $this->assertCount($expectedCount, $result);
    }

    public static function courseManagerCollectionProvider(): \Generator
    {
        yield 'empty collection' => [
            'courseManagerData' => [],
            'expectedResult' => [],
            'expectedCount' => 0,
        ];

        yield 'single course' => [
            'courseManagerData' => [
                ['userId' => 456, 'courseId' => 99],
            ],
            'expectedResult' => [
                ['id' => 99],
            ],
            'expectedCount' => 1,
        ];
        yield 'multiple courses' => [
            'courseManagerData' => [
                ['userId' => 123, 'courseId' => 1],
                ['userId' => 123, 'courseId' => 2],
                ['userId' => 123, 'courseId' => 15],
            ],
            'expectedResult' => [
                ['id' => 1],
                ['id' => 2],
                ['id' => 15],
            ],
            'expectedCount' => 3,
        ];
    }
}
