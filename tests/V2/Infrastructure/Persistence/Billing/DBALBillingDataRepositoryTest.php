<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Billing;

use App\Tests\V2\Domain\Billing\BillingDataRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\Billing\BillingDataRepository;
use App\V2\Infrastructure\Persistence\Billing\DBALBillingDataRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;

class DBALBillingDataRepositoryTest extends BillingDataRepositoryTestCase
{
    private const string TABLE_NAME = 'billing_data';
    private Connection $connection;

    /**
     * @throws Exception
     * @throws SchemaException
     */
    protected function getRepository(): BillingDataRepository
    {
        [$this->connection] = DBALConnectionFactory::create();

        $this->createTable();

        return new DBALBillingDataRepository(
            $this->connection,
            self::TABLE_NAME
        );
    }

    /**
     * @throws SchemaException
     * @throws Exception
     */
    private function createTable(): void
    {
        $schema = new Schema();
        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('id', 'string', ['length' => 36]);
        $table->addColumn('user_id', 'integer');
        $table->addColumn('tin', 'string', ['length' => 50]);
        $table->addColumn('first_name', 'string', ['length' => 255]);
        $table->addColumn('last_name', 'string', ['length' => 255]);
        $table->addColumn('address', 'text');
        $table->addColumn('postal_code', 'string', ['length' => 20]);
        $table->addColumn('city', 'string', ['length' => 255]);
        $table->addColumn('country', 'string', ['length' => 255]);
        $table->addColumn('metadata', 'text');
        $table->setPrimaryKey(['id']);
        $table->addIndex(['user_id'], 'idx_billing_data_user_id');
        $table->addIndex(['tin'], 'idx_billing_data_tin');

        $this->connection->createSchemaManager()->createTable($table);
    }

    /**
     * @throws Exception
     */
    private function dropTable(): void
    {
        $this->connection->executeStatement('DROP TABLE IF EXISTS `' . self::TABLE_NAME . '`');
    }

    protected function tearDown(): void
    {
        $this->dropTable();
        parent::tearDown();
    }
}
