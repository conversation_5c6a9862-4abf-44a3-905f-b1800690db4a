<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\LTI\Transformer;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\LTI\LtiRegistrationCriteria;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Infrastructure\LTI\Transformer\LtiRegistrationCriteriaTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class LtiRegistrationCriteriaTransformerTest extends TestCase
{
    /**
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    #[DataProvider('provideFromArray')]
    public function testFromArray(array $data, LtiRegistrationCriteria $expected): void
    {
        $this->assertEquals($expected, LtiRegistrationCriteriaTransformer::fromArray($data));
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     */
    public static function provideFromArray(): \Generator
    {
        $id = UuidMother::create();
        yield 'only id' => [
            'data' => [
                'id' => $id->value(),
            ],
            'expected' => LtiRegistrationCriteria::createById($id),
        ];

        yield 'only name' => [
            'data' => [
                'name' => 'registration 1',
            ],
            'expected' => LtiRegistrationCriteria::createEmpty()->filterBySearchString('registration 1'),
        ];

        yield 'only client id' => [
            'data' => [
                'client_id' => 'registration_1',
            ],
            'expected' => LtiRegistrationCriteria::createEmpty()->filterByClientId('registration_1'),
        ];

        yield 'all params' => [
            'data' => [
                'id' => $id->value(),
                'name' => 'registration 1',
                'client_id' => 'registration_1',
            ],
            'expected' => LtiRegistrationCriteria::createById($id)
                ->filterBySearchString('registration 1')
                ->filterByClientId('registration_1'),
        ];
    }
}
