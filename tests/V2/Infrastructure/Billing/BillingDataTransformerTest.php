<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Billing;

use App\Tests\V2\Mother\Billing\BillingDataMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Billing\BillingData;
use App\V2\Infrastructure\Billing\BillingDataTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class BillingDataTransformerTest extends TestCase
{
    #[DataProvider('validBillingDataProvider')]
    public function testFromBillingDataToArrayWithValidInputs(
        BillingData $billingData,
        array $expectedArray,
    ): void {
        $result = BillingDataTransformer::fromBillingDataToArray($billingData);

        $this->assertIsArray($result);
        $this->assertEquals($expectedArray, $result);
    }

    public function testFromBillingDataToArrayMethodExists(): void
    {
        $this->assertTrue(method_exists(BillingDataTransformer::class, 'fromBillingDataToArray'));
        $this->assertTrue(\is_callable([BillingDataTransformer::class, 'fromBillingDataToArray']));
    }

    #[DataProvider('returnValueConsistencyProvider')]
    public function testFromBillingDataToArrayReturnValueConsistency(BillingData $billingData): void
    {
        $result1 = BillingDataTransformer::fromBillingDataToArray($billingData);
        $result2 = BillingDataTransformer::fromBillingDataToArray($billingData);

        $this->assertIsArray($result1);
        $this->assertIsArray($result2);
        $this->assertEquals($result1, $result2);
    }

    #[DataProvider('dataTransformationProvider')]
    public function testDataTransformationAccuracy(
        BillingData $billingData,
        string $expectedId,
        int $expectedUserId,
        string $expectedTin,
        string $expectedFirstName,
        string $expectedLastName,
        string $expectedAddress,
        string $expectedPostalCode,
        string $expectedCity,
        string $expectedCountry,
        array $expectedMetadata,
    ): void {
        $result = BillingDataTransformer::fromBillingDataToArray($billingData);

        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('user_id', $result);
        $this->assertArrayHasKey('tin', $result);
        $this->assertArrayHasKey('first_name', $result);
        $this->assertArrayHasKey('last_name', $result);
        $this->assertArrayHasKey('address', $result);
        $this->assertArrayHasKey('postal_code', $result);
        $this->assertArrayHasKey('city', $result);
        $this->assertArrayHasKey('country', $result);
        $this->assertArrayHasKey('metadata', $result);

        $this->assertSame($expectedId, $result['id']);
        $this->assertSame($expectedUserId, $result['user_id']);
        $this->assertSame($expectedTin, $result['tin']);
        $this->assertSame($expectedFirstName, $result['first_name']);
        $this->assertSame($expectedLastName, $result['last_name']);
        $this->assertSame($expectedAddress, $result['address']);
        $this->assertSame($expectedPostalCode, $result['postal_code']);
        $this->assertSame($expectedCity, $result['city']);
        $this->assertSame($expectedCountry, $result['country']);
        $this->assertSame($expectedMetadata, $result['metadata']);
    }

    #[DataProvider('edgeCasesProvider')]
    public function testFromBillingDataToArrayWithEdgeCases(
        BillingData $billingData,
        array $expectedArray,
    ): void {
        $result = BillingDataTransformer::fromBillingDataToArray($billingData);

        $this->assertIsArray($result);
        $this->assertEquals($expectedArray, $result);
    }

    /**
     * @throws \Exception
     */
    public static function validBillingDataProvider(): \Generator
    {
        $id1 = UuidMother::create();
        $userId1 = IdMother::create();

        yield 'basic_billing_data' => [
            'billingData' => BillingDataMother::create(
                id: $id1,
                userId: $userId1,
                tin: '12345678A',
                firstName: 'John',
                lastName: 'Doe',
                address: '123 Main Street',
                postalCode: '12345',
                city: 'Madrid',
                country: 'Spain',
                metadata: ['key' => 'value']
            ),
            'expectedArray' => [
                'id' => $id1->value(),
                'user_id' => $userId1->value(),
                'tin' => '12345678A',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'country' => 'Spain',
                'metadata' => ['key' => 'value'],
            ],
        ];

        $id2 = UuidMother::create();
        $userId2 = IdMother::create();

        yield 'billing_data_with_different_values' => [
            'billingData' => BillingDataMother::create(
                id: $id2,
                userId: $userId2,
                tin: '87654321B',
                firstName: 'Jane',
                lastName: 'Smith',
                address: '456 Oak Avenue',
                postalCode: '54321',
                city: 'Barcelona',
                country: 'Spain',
                metadata: ['type' => 'business', 'vat' => true]
            ),
            'expectedArray' => [
                'id' => $id2->value(),
                'user_id' => $userId2->value(),
                'tin' => '87654321B',
                'first_name' => 'Jane',
                'last_name' => 'Smith',
                'address' => '456 Oak Avenue',
                'postal_code' => '54321',
                'city' => 'Barcelona',
                'country' => 'Spain',
                'metadata' => ['type' => 'business', 'vat' => true],
            ],
        ];
    }

    /**
     * @throws \Exception
     */
    public static function returnValueConsistencyProvider(): \Generator
    {
        yield 'consistency_test_1' => [
            'billingData' => BillingDataMother::create(
                id: UuidMother::create(),
                userId: IdMother::create()
            ),
        ];

        yield 'consistency_test_2' => [
            'billingData' => BillingDataMother::create(
                id: UuidMother::create(),
                userId: IdMother::create(),
                tin: 'B12345678',
                firstName: 'Test',
                lastName: 'User'
            ),
        ];
    }

    /**
     * @throws \Exception
     */
    public static function dataTransformationProvider(): \Generator
    {
        $id = UuidMother::create();
        $userId = IdMother::create();

        yield 'transformation_accuracy_test' => [
            'billingData' => BillingDataMother::create(
                id: $id,
                userId: $userId,
                tin: 'X1234567L',
                firstName: 'Alice',
                lastName: 'Johnson',
                address: '789 Pine Street',
                postalCode: '67890',
                city: 'Valencia',
                country: 'Spain',
                metadata: ['company' => 'ACME Corp', 'department' => 'IT']
            ),
            'expectedId' => $id->value(),
            'expectedUserId' => $userId->value(),
            'expectedTin' => 'X1234567L',
            'expectedFirstName' => 'Alice',
            'expectedLastName' => 'Johnson',
            'expectedAddress' => '789 Pine Street',
            'expectedPostalCode' => '67890',
            'expectedCity' => 'Valencia',
            'expectedCountry' => 'Spain',
            'expectedMetadata' => ['company' => 'ACME Corp', 'department' => 'IT'],
        ];
    }

    /**
     * @throws \Exception
     */
    public static function edgeCasesProvider(): \Generator
    {
        $id = UuidMother::create();
        $userId = IdMother::create();

        yield 'empty_metadata' => [
            'billingData' => BillingDataMother::create(
                id: $id,
                userId: $userId,
                metadata: []
            ),
            'expectedArray' => [
                'id' => $id->value(),
                'user_id' => $userId->value(),
                'tin' => '12345678A',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'country' => 'Spain',
                'metadata' => [],
            ],
        ];

        $id2 = UuidMother::create();
        $userId2 = IdMother::create();

        yield 'complex_metadata' => [
            'billingData' => BillingDataMother::create(
                id: $id2,
                userId: $userId2,
                metadata: [
                    'nested' => ['key' => 'value'],
                    'array' => [1, 2, 3],
                    'boolean' => true,
                    'null' => null,
                ]
            ),
            'expectedArray' => [
                'id' => $id2->value(),
                'user_id' => $userId2->value(),
                'tin' => '12345678A',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'country' => 'Spain',
                'metadata' => [
                    'nested' => ['key' => 'value'],
                    'array' => [1, 2, 3],
                    'boolean' => true,
                    'null' => null,
                ],
            ],
        ];

        $id3 = UuidMother::create();
        $userId3 = IdMother::create();

        yield 'special_characters_in_strings' => [
            'billingData' => BillingDataMother::create(
                id: $id3,
                userId: $userId3,
                tin: 'ñ1234567Ñ',
                firstName: 'José María',
                lastName: 'García-López',
                address: 'Calle de la Constitución, 123',
                city: 'Málaga',
                country: 'España'
            ),
            'expectedArray' => [
                'id' => $id3->value(),
                'user_id' => $userId3->value(),
                'tin' => 'ñ1234567Ñ',
                'first_name' => 'José María',
                'last_name' => 'García-López',
                'address' => 'Calle de la Constitución, 123',
                'postal_code' => '12345',
                'city' => 'Málaga',
                'country' => 'España',
                'metadata' => [],
            ],
        ];
    }
}
