<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait\Endpoints\Admin;

class LtiEndpoints
{
    public static function postLtiRegistrationEndpoint(): string
    {
        return '/api/v2/admin/lti/registrations';
    }

    public static function postLtiPlatformEndpoint(string $registrationId): string
    {
        return \sprintf('/api/v2/admin/lti/registrations/%s/platform', $registrationId);
    }

    public static function postLtiToolEndpoint(string $registrationId): string
    {
        return \sprintf('/api/v2/admin/lti/registrations/%s/tool', $registrationId);
    }

    public static function postLtiDeploymentEndpoint(string $registrationId): string
    {
        return \sprintf('/api/v2/admin/lti/registrations/%s/deployments', $registrationId);
    }

    public static function getLtiRegistrationsEndpoint(): string
    {
        return '/api/v2/admin/lti/registrations';
    }

    public static function getLtiRegistrationEndpoint(
        string $id,
        bool $withTool = false,
        bool $withPlatform = false,
        bool $withDeployments = false,
    ): string {
        $url = \sprintf('/api/v2/admin/lti/registrations/%s', $id);

        $params = [];
        if ($withTool) {
            $params['tool'] = 'true';
        }

        if ($withPlatform) {
            $params['platform'] = 'true';
        }

        if ($withDeployments) {
            $params['deployments'] = 'true';
        }

        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        return $url;
    }

    public static function getLaunchLtiChapter(int $courseId, int $chapterId): string
    {
        return \sprintf('/api/v2/admin/courses/%d/chapter/%d/lti-launch', $courseId, $chapterId);
    }
}
