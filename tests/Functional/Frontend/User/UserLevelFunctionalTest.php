<?php

declare(strict_types=1);

namespace App\Tests\Functional\Frontend\User;

use App\Entity\UserLogin;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendUserEndpoints;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class UserLevelFunctionalTest extends FunctionalTestCase
{
    private ?int $defaultUserPoints = null;

    /**
     * @dataProvider userCorrectPointsAndLevelProvider
     */
    public function testUserLevelWithCorrectPointsAndLevel(
        int $userPoints,
        string $level,
        ?int $stars = null
    ): void {
        $responseData = $this->getUserLevelApiResponseData($userPoints);
        $this->assertEquals($level, $responseData['level']);
        if ($stars) {
            $this->assertArrayHasKey('stars', $responseData);
            $this->assertEquals($stars, round($responseData['stars']));
        }
    }

    public static function userCorrectPointsAndLevelProvider(): \Generator
    {
        yield 'User with 4000 points and level3' => [
            'userPoints' => 4000,
            'level' => 'Intermedio',
        ];

        // Formula in UserLevelService, line 32.
        // 10000 - 8001 = 1999 /1000 = 1.999 / 2 = 0.999
        yield 'User with 10000 points, level master and 10 stars' => [
            'userPoints' => 10000,
            'level' => 'Maestro',
            'stars' => 1,
        ];
    }

    private function getUserLevelApiResponseData(int $points): array
    {
        $user = $this->getDefaultUser();
        $userToken = $this->loginAndGetToken();
        $this->defaultUserPoints = $user->getPoints();
        $user->setPoints($points);

        $this->save();

        $response = $this->makeFrontendApiRequest(
            'GET',
            FrontendUserEndpoints::userLevelEndpoint(),
            [],
            [],
            [],
            $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        return $this->extractResponseData($response);
    }

    private function save()
    {
        $em = $this->getEntityManager();
        try {
            $em->flush();
        } catch (OptimisticLockException $e) {
        } catch (ORMException $e) {
            $this->fail($e->getMessage());
        }
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([UserLogin::class]);
        $user = $this->getDefaultUser();
        $user->setPoints($this->defaultUserPoints);
        $this->save();

        parent::tearDown();
    }
}
