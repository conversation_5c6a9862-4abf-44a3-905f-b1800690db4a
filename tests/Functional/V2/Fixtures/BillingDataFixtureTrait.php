<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\Billing\BillingDataMother;
use App\V2\Domain\Billing\BillingData;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\Uuid;

trait BillingDataFixtureTrait
{
    /**
     * Creates and stores an BillingData entity in the repository.
     *
     * @throws \Exception
     */
    private function setAndGetBillingDataInRepository(
        ?Uuid $id = null,
        ?Id $userId = null,
        ?string $tin = null,
        ?string $firstName = null,
        ?string $lastName = null,
        ?string $address = null,
        ?string $postalCode = null,
        ?string $city = null,
        ?string $country = null,
        ?array $metadata = null,
    ): BillingData {
        $billingData = BillingDataMother::create(
            id: $id,
            userId: $userId,
            tin: $tin,
            firstName: $firstName,
            lastName: $lastName,
            address: $address,
            postalCode: $postalCode,
            city: $city,
            country: $country,
            metadata: $metadata,
        );

        $this->client->getContainer()->get('App\V2\Domain\Billing\BillingDataRepository')
            ->put($billingData);

        return $billingData;
    }
}
