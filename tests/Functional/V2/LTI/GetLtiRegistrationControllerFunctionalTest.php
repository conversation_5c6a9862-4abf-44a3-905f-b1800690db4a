<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\LTI;

use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\LtiEndpoints;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\LtiDeploymentFixtureTrait;
use App\Tests\Functional\V2\Fixtures\LtiPlatformFixtureTrait;
use App\Tests\Functional\V2\Fixtures\LtiRegistrationFixtureTrait;
use App\Tests\Functional\V2\Fixtures\LtiToolFixtureTrait;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class GetLtiRegistrationControllerFunctionalTest extends FunctionalTestCase
{
    use UserHelperTrait;
    use LtiRegistrationFixtureTrait;
    use LtiToolFixtureTrait;
    use LtiPlatformFixtureTrait;
    use LtiDeploymentFixtureTrait;

    private array $userIds = [];
    private const string SUPER_ADMIN_EMAIL = '<EMAIL>';

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $superAdminUser = $this->createAndGetUser(
            roles: [User::ROLE_SUPER_ADMIN],
            email: self::SUPER_ADMIN_EMAIL,
        );

        $this->userIds[] = $superAdminUser->getId();
    }

    /**
     * @throws InvalidUuidException
     */
    public function testGetRegistration(): void
    {
        $registration = $this->setAndGetLtiRegistrationInRepository();
        $platform = $this->setAndGetLtiPlatformInRepository(
            registrationId: $registration->getId(),
            name: 'Platform 1',
            audience: 'Platform 1 Audience',
            oidcAuthenticationUrl: new Url('https://example-platform.com/lti1p3/oidc/authentication'),
            oauth2AccessTokenUrl: new Url('https://example-platform.com/token'),
            jwksUrl: new Url('https://example-platform.com/lti1p3/jwks'),
        );
        $tool = $this->setAndGetLtiToolInRepository(
            registrationId: $registration->getId(),
            name: 'Tool 1',
            audience: 'Tool 1 Audience',
            oidcInitiationUrl: new Url('https://example-tool.com/lti1p3/oidc/initiation'),
            launchUrl: new Url('https://example-tool.com/lti1p3/launch'),
            deepLinkingUrl: new Url('https://example-tool.com/lti1p3/deep-linking'),
            jwksUrl: new Url('https://example-tool.com/lti1p3/jwks'),
        );
        $deployment = $this->setAndGetLtiDeploymentInRepository(
            registrationId: $registration->getId(),
            name: 'Deployment 1',
            deploymentId: 'deployment-1',
        );

        $token = $this->loginAndGetToken(
            email: self::SUPER_ADMIN_EMAIL,
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: LtiEndpoints::getLtiRegistrationEndpoint(
                id: $registration->getId()->value(),
                withTool: true,
                withPlatform: true,
                withDeployments: true,
            ),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertEquals($registration->getId()->value(), $data['id']);
        $this->assertEquals($registration->getName(), $data['name']);
        $this->assertEquals($registration->getClientId(), $data['client_id']);

        $this->assertArrayHasKey('tool', $data);
        $this->assertEquals([
            'id' => $tool->getId()->value(),
            'name' => 'Tool 1',
            'audience' => 'Tool 1 Audience',
            'oidc_initiation_url' => 'https://example-tool.com/lti1p3/oidc/initiation',
            'launch_url' => 'https://example-tool.com/lti1p3/launch',
            'deep_linking_url' => 'https://example-tool.com/lti1p3/deep-linking',
            'jwks_url' => 'https://example-tool.com/lti1p3/jwks',
        ], $data['tool']);

        $this->assertArrayHasKey('platform', $data);
        $this->assertEquals([
            'id' => $platform->getId()->value(),
            'name' => 'Platform 1',
            'audience' => 'Platform 1 Audience',
            'oidc_authentication_url' => 'https://example-platform.com/lti1p3/oidc/authentication',
            'oauth2_access_token_url' => 'https://example-platform.com/token',
            'jwks_url' => 'https://example-platform.com/lti1p3/jwks',
        ], $data['platform']);

        $this->assertArrayHasKey('deployments', $data);
        $this->assertCount(1, $data['deployments']);
        $this->assertEquals([
            [
                'id' => $deployment->getId()->value(),
                'name' => 'Deployment 1',
                'deployment_id' => 'deployment-1',
            ],
        ], $data['deployments']);

        $response = $this->makeRequest(
            method: 'GET',
            uri: LtiEndpoints::getLtiRegistrationEndpoint(
                id: $registration->getId()->value(),
                withDeployments: true,
            ),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertEquals($registration->getId()->value(), $data['id']);
        $this->assertEquals($registration->getName(), $data['name']);
        $this->assertEquals($registration->getClientId(), $data['client_id']);
        $this->assertArrayNotHasKey('tool', $data);
        $this->assertArrayNotHasKey('platform', $data);

        $this->assertArrayHasKey('deployments', $data);
        $this->assertCount(1, $data['deployments']);
        $this->assertEquals([
            [
                'id' => $deployment->getId()->value(),
                'name' => 'Deployment 1',
                'deployment_id' => 'deployment-1',
            ],
        ], $data['deployments']);

        $response = $this->makeRequest(
            method: 'GET',
            uri: LtiEndpoints::getLtiRegistrationEndpoint(
                id: $registration->getId()->value(),
                withTool: true,
            ),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertEquals($registration->getId()->value(), $data['id']);
        $this->assertEquals($registration->getName(), $data['name']);
        $this->assertEquals($registration->getClientId(), $data['client_id']);
        $this->assertArrayNotHasKey('deployments', $data);
        $this->assertArrayNotHasKey('platform', $data);

        $this->assertArrayHasKey('tool', $data);
        $this->assertEquals([
            'id' => $tool->getId()->value(),
            'name' => 'Tool 1',
            'audience' => 'Tool 1 Audience',
            'oidc_initiation_url' => 'https://example-tool.com/lti1p3/oidc/initiation',
            'launch_url' => 'https://example-tool.com/lti1p3/launch',
            'deep_linking_url' => 'https://example-tool.com/lti1p3/deep-linking',
            'jwks_url' => 'https://example-tool.com/lti1p3/jwks',
        ], $data['tool']);
    }

    public function testRegistrationNotFound(): void
    {
        $token = $this->loginAndGetToken(
            email: self::SUPER_ADMIN_EMAIL,
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: LtiEndpoints::getLtiRegistrationEndpoint(UuidMother::create()->value()),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('LTI registration could not be found.', $content['message']);
    }

    public function testBadRequest(): void
    {
        $token = $this->loginAndGetToken(
            email: self::SUPER_ADMIN_EMAIL,
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: LtiEndpoints::getLtiRegistrationEndpoint('invalid-uuid'),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
        $this->assertArrayHasKey('metadata', $content);
        $this->assertEquals([
            'violations' => [
                '[uuid]' => 'This is not a valid UUID.',
            ],
        ], $content['metadata']);
    }

    public function testForbidden(): void
    {
        $token = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'GET',
            uri: LtiEndpoints::getLtiRegistrationEndpoint(UuidMother::create()->value()),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    public function testUnauthorized(): void
    {
        $response = $this->makeRequest(
            method: 'GET',
            uri: LtiEndpoints::getLtiRegistrationEndpoint(UuidMother::create()->value()),
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds($this->userIds);
        parent::tearDown();
    }
}
