<?php

declare(strict_types=1);

namespace App\Tests\Campus\Service\Games;

use App\Campus\Games\GameStrategyInterface;
use PHPUnit\Framework\TestCase;

abstract class TestGameStrategyInterface extends TestCase
{
    /**
     * @dataProvider getValidData
     */
    public function testCalculateGamePoints(array $data, $args, $expectedPoints): void
    {
        $strategy = $this->getStrategy();
        $this->assertEqualsWithDelta($expectedPoints, $strategy->calculateGamePoints($data, $args), 0.01);
    }

    abstract public function getStrategy(): GameStrategyInterface;

    abstract public static function getValidData(): \Generator;
}
