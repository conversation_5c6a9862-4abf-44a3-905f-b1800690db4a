{% if conexions %}
  <div class="content-panel p-3 course-contents">
    <h2>{{ 'report.annnouncement.conexions'|trans({}, 'messages') }}</h2>

    <table class="table datagrid with-rounded-top" width="100%">
      <thead class="thead-light">
        <tr>
          <th>
            <span>{{ 'user.configureFields.date'|trans({}, 'messages') }}</span>
          </th>
          <th>
            <span>Ip</span>
          </th>
          <th>{{ 'report.announcement.time_conexion'|trans({}, 'messages') }}</th>
          <th>{{ 'report.announcement.init_finish'|trans({}, 'messages') }}</th>
        </tr>
      </thead>

      <tbody>
        {% for conexion in conexions %}
          <tr>
            <td>{{ conexion.day }}</td>
            <td>{{ conexion.ip }}</td>
            <td>{{ conexion.time }}</td>
            <td>{{ conexion.initFin }}</td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
{% endif %}
