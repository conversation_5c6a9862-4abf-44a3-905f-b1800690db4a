{% extends 'base.html.twig' %}

{% block title %}
  Log in!
{% endblock %}

{% block stylesheets %}
  {{ parent() }}
  {{ encore_entry_link_tags('app') }}
{% endblock %}

{% block body %}
  <div id="login" class="container h-100">
    <div class="row h-100 justify-content-center align-items-center">
      <form method="post" class="col-12 col-md-6 p-4 loginForm">
        {% if error %}
          <div class="alert alert-danger">{{ error.messageKey|trans(error.messageData, 'security') }}</div>
        {% endif %}

        {% if app.user %}
          <div class="mb-3">
            You are logged in as {{ app.user.username }}, <a href="{{ path('app_logout') }}">Logout</a>
          </div>
        {% endif %}

        <div class="logo mb-3"></div>

        <h1 class="h3 mb-3 font-weight-normal">{{ 'security.login_title'|trans({}, 'messages') }}</h1>
        <div class="form-group">
          <label for="inputEmail">{{ 'user.label_in_singular'|trans({}, 'messages') }}</label>
          <input type="text" value="{{ last_username }}" name="email" id="inputEmail" class="form-control" required autofocus />
        </div>
        <div class="form-group">
          <label for="inputPassword">{{ 'security.password'|trans({}, 'messages') }}</label>
          <input type="password" name="password" id="inputPassword" class="form-control" required />
        </div>
        <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}" />
        <input type="hidden" name="_remember_me" value="1" />

        <div class="groupButton">
          <button class="button buttonPrimary" type="submit">{{ 'security.login_button_login'|trans({}, 'messages') }}</button>
          <a class="button buttonPrimary" href="#">Usuario / Contraseña</a>
          <input type="hidden" name="ref" value="ldap">
        </div>
      </form>
    </div>
  </div>
{% endblock %}
