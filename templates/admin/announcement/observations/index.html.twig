<div class="content-panel pb-3">
    <div class="page-actions pt-3 pr-3 text-right mb-3">
        {% if 'ROLE_SUBSIDIZER' not in app.user.roles %}
        <a class="action-new btn btn-primary" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\AnnouncementObservationCrudController')
            .setAction('new')
            .set('announcementId', announcement.id)
            .set('referrer', referrerTask) }}"
        >
           {{ 'add'|trans({}) }}
        </a>
        {% endif %}
    </div>
    {% if observations %}
        <table class="table mt-4">
            <thead>
            <tr>
                <th>{{ 'common_areas.created_by'|trans({}) }}</th>
                <th>{{ 'common_areas.created_at'|trans({}) }}</th>
                <th></th>
            </tr>
            </thead>
            <tbody>
            {% for observation in observations %}
                <tr>
                    <td>{{ observation.createdBy.fullName }}</td>
                    <td>{{ observation.createdAt|date }}</td>
                    <td class="text-right">
                        <a href="{{
                        ea_url()
                            .unsetAll()
                            .setController('App\\Controller\\Admin\\AnnouncementObservationCrudController')
                            .setAction('detail')
                            .setEntityId(observation.id)
                            .set('referrer', referrerTask) }}"
                           class="btn btn-secondary btn-sm"
                        >
                            <i class="fa fa-eye"></i>
                        </a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div >
            <div class="card text-center">
                <div class="card-header" style="height:20rem">
                    <h3 style="padding-top:8rem"> {{ 'no_content'|trans({}, 'messages') }}</h3>
                </div>
            </div>
        </div>
    {% endif %}

</div>
