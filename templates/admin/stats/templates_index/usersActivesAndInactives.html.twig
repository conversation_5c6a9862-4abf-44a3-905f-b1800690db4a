<div class="users-actives-and-less">

	<div v-if="general && ((general.usersMoreActives && general.usersMoreActives.length))" class="actives">
		<div v-if="general.usersMoreActives && general.usersMoreActives.length" class="course-list">
			<h2 class="subtitle">
				<i class="far fa-user"></i>
				{{ 'stats.users_more_actives'|trans({}, 'messages',  app.user.locale) }}
			</h2>
			<ol>
				<li>
					<span class="info nps-percentaje">{{ 'menu.courses_managment.courses'|trans({}, 'messages',  app.user.locale) }}
					</span>
				</li>
				<li v-for="user in general.usersMoreActives">
					<span class="success nps-percentaje">${user.finishedCourses}
					</span>
					<a :href="user.link">
						${user.name}</a>
				</li>
			</ol>
		</div>
	</div>

	<div v-if="general && ((general.usersLessActives && general.usersLessActives.length))" class="inactives">
		<div v-if="general.usersLessActives && general.usersLessActives.length" class="course-list">
			<h2 class="subtitle">
				<i class="far fa-user"></i>
				{{ 'stats.users_less_actives'|trans({}, 'messages',  app.user.locale) }}
			</h2>
			<ol>
				<li>
					<span class="info nps-percentaje">{{ 'menu.courses_managment.courses'|trans({}, 'messages',  app.user.locale) }}
					</span>
				</li>
				<li v-for="user in general.usersLessActives">
					<span class="error nps-percentaje">${user.finishedCourses}
					</span>
					<a :href="user.link">
						${user.name}</a>
				</li>
			</ol>
		</div>
	</div>
</div>
