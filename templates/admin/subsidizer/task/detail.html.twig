{% extends '@!EasyAdmin/page/content.html.twig' %}

{% block head_stylesheets %}
  {{ parent() }}
  {{ encore_entry_link_tags('task-course') }}
{% endblock %}

{% block content_title %}
  {{ task.course.name }}
{% endblock %}

{% block main %}
  <div class="card" style="width: 100%;">
    <div class="card-body">
      <h5 class="card-title">{{ task.title }}</h5>
      <h6 class="card-subtitle mb-2 text-muted"></h6>
      <p class="card-text">{{ task.description|raw }}</p>
    </div>
  </div>

  <div id="taskCourse">
    <div>
      <list-files-task :origin="'SUBSIDIZER'" />
    </div>

    <div>
      <history-task :origin="'SUBSIDIZER'" />
    </div>
  </div>
{% endblock %}

{% block body_javascript %}
  <script>
			let idCourse 			   = {{ task.course.id | json_encode | raw }};
			let idTask 				   = {{ task.id | json_encode | raw }};
    </script>
  {{ parent() }}

  {{ encore_entry_script_tags('task-course') }}
{% endblock %}
