{% extends '@!EasyAdmin/crud/detail.html.twig' %}

{% block content_title %}
    Pregunta - {{ question.text }}
{% endblock content_title %}

{% block head_stylesheets %}
    {{ parent() }}

    <style>
        .select2-container--bootstrap .select2-selection {
            max-width: none !important;
        }

        .img-fluid {
            box-shadow: 0 0 0 2px var(--white), 0 0 4px 1px var(--gray-600);
            margin-bottom: 6px;
            margin-top: 6px;
            max-height: 100px;
            max-width: 200px;
        }
    </style>
{% endblock %}

{% block main %}
    <div id="questions-detail" data-category="{{ question.id }}">
        <div class="content-panel">
            <div class="field-form_panel">
                <div class="content-panel">
                    <div class="content-panel-header">
                        {{ 'course.configureFields.basic_information'|trans({}, 'messages') }}
                    </div>

                    <div class="content-panel-body without-footer without-padding">
                        <dl class="datalist">

                            <div class="data-row  field-text">
                                <dd>{{ 'challenges.question'|trans({}, 'messages') }}</dd>
                                <dt>
                                    <span title="{{ question.text }}">
                                        {{ question.text }}
                                    </span>
                                </dt>
                            </div>

                            <div class="data-row  field-text">
                                <dd>{{ 'challenges.challenges'|trans({}, 'messages') }}</dd>
                                <dt>
                                    <span title="{{ question.text }}">
                                        {{ challenge.title }}
                                    </span>
                                </dt>
                            </div>

                            <div class="data-row  field-text">
                                <dd>{{ 'chapter.configureFields.image'|trans({}, 'messages') }}</dd>
                                <dt>
                                    <span>
                                        {% if(question.image) %}
                                            <img src="{{question.image }}" class="img-fluid">
                                        {% else %}
                                            {{ 'challenges.no_images'|trans({}, 'messages') }}
                                        {% endif %}
                                    </span>
                                </dt>
                            </div>

                            <div class="data-row with-background field-association">
                                <dd>{{ 'questions.manageresponse'|trans({}, 'messages') }}</dd>
                                <dt>
                                    <span class="badge badge-secondary">{{ question.challengeAnswers | length }}</span>
                                    {% if question.challengeAnswers | length > 0 %}
                                        <a href="{{ ea_url().unsetAll()
                                            .setController('App\\Controller\\Admin\\ChallengeAnswersCrudController')
                                            .setAction('index')
                                            .set('filters[category][comparison]', '=')
                                            .set('filters[category][value]', question.id) }}">
                                            {{ 'questions.see_answers'|trans({}, 'messages') }}
                                        </a>
                                    {% endif %}
                                </dt>
                            </div>

                        </dl>
                    </div>
                </div>
            </div>
        </div>


        <ul class="nav nav-tabs" id="courseTab" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link active" id="questions-tab" data-toggle="tab" href="#questions" role="tab"
                   aria-controls="questions" aria-selected="true">{% trans %}Answers{% endtrans %}</a>
            </li>

        </ul>

        <div class="tab-content" id="questionsTabContent">
            <div class="tab-pane fade show active" id="questions" role="tabpanel" aria-labelledby="questions-tab">
                <div class="content-panel mb-2">
                    <div class="content-panel-body with-rounded-top without-padding ">
                        <table class="table datagrid with-rounded-top">
                            <thead>
                            <tr>
                                <th><span>{{ 'news.form.text'|trans({}, 'messages') }}</span></th>

                                <th><span>{{ 'trueorFalse.configureFields.true'|trans({}, 'messages') }}</span></th>


                                <th><span></span></th>
                            </tr>
                            </thead>

                            <tbody>
                            {% for answer in answers %}
                                <tr>
                                    <td>
                                        {{ answer.text }}
                                    </td>

                                    {% if(answer.correct) %}
                                        <td>
                                            Si
                                        </td>

                                    {% else %}

                                        <td>
                                            No
                                        </td>

                                    {% endif %}


                                    <td class="actions">
                                        <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\ChallengeAnswersCrudController').setAction('detail').setEntityId(answer.id) }}">
                                            {% trans %}Show{% endtrans %}
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </div>

    </div>


{% endblock main %}
