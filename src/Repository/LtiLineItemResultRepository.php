<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\LtiLineItemResult;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use OAT\Library\Lti1p3Ags\Model\Result\ResultCollection;
use OAT\Library\Lti1p3Ags\Model\Result\ResultCollectionInterface;
use OAT\Library\Lti1p3Ags\Model\Result\ResultInterface;

/**
 * @extends ServiceEntityRepository<LtiLineItemResult>
 *
 * @method LtiLineItemResult|null find($id, $lockMode = null, $lockVersion = null)
 * @method LtiLineItemResult|null findOneBy(array $criteria, array $orderBy = null)
 * @method LtiLineItemResult[]    findAll()
 * @method LtiLineItemResult[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LtiLineItemResultRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LtiLineItemResult::class);
    }

    public function add(LtiLineItemResult $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(LtiLineItemResult $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findCollectionByLineItemIdentifier(string $lineItemIdentifier, ?int $limit = null, ?int $offset = null): ResultCollectionInterface
    {
        $lineItemResults = $this->findBy(['scoreOf' => $lineItemIdentifier]);
        $results = [];

        foreach ($lineItemResults as $result) {
            $results[] = $result->getResult();
        }

        return new ResultCollection(
            \array_slice($lineItemResults, $offset ?: 0, $limit),
            $limit && ($limit + $offset) < count($lineItemResults)
        );
    }

    public function findByLineItemIdentifierAndUserIdentifier(string $lineItemIdentifier, string $userIdentifier): ?ResultInterface
    {
        $result = $this->findOneBy([
            'scoreOf' => $lineItemIdentifier,
            'userId' => $userIdentifier
        ]);

        return $result ? $result->getResult() : null;
    }
}
