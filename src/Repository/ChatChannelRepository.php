<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ChatChannel;
use App\Entity\ChatChannelUser;
use App\Entity\ChatServer;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;

/**
 * @extends ServiceEntityRepository<ChatChannel>
 *
 * @method ChatChannel|null find($id, $lockMode = null, $lockVersion = null)
 * @method ChatChannel|null findOneBy(array $criteria, array $orderBy = null)
 * @method ChatChannel[]    findAll()
 * @method ChatChannel[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ChatChannelRepository extends ServiceEntityRepository
{
    private LoggerInterface $logger;

    public function __construct(ManagerRegistry $registry, LoggerInterface $logger)
    {
        parent::__construct($registry, ChatChannel::class);
        $this->logger = $logger;
    }

    public function add(ChatChannel $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ChatChannel $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function isUserInChannel(ChatChannel $channel, User $user): bool
    {
        $inChannel = $this->createQueryBuilder('c')
            ->join('c.chatChannelUsers', 'cu')
            ->where('c =:channel')
            ->andWhere('cu.user =:user')
            ->setParameter('channel', $channel)
            ->setParameter('user', $user)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        return null != $inChannel;
    }

    public function createNormalChannel(
        ChatServer $server,
        ?ChatChannel $parent = null,
        string $name = 'default',
        ?string $type = null,
        ?\DateTime $from = null,
        ?\DateTime $to = null
    ): ChatChannel {
        $channel = new ChatChannel();
        $channel->setServer($server)->setName($name);
        if ($parent) {
            $channel->setParent($parent);
        }
        if ($type) {
            $channel->setType($type);
        }
        if (!empty($from)) {
            $channel->setAvailableFrom($from);
        }
        if (!empty($to)) {
            $channel->setAvailableUntil($to);
        }

        $this->_em->persist($channel);
        $this->_em->flush();

        return $channel;
    }

    /**
     * @param User[] $users
     */
    public function createDirectChannel(
        ChatServer $server,
        ?ChatChannel $parent = null,
        string $name = 'default',
        array $users = [],
        ?\DateTimeInterface $from = null,
        ?\DateTimeInterface $to = null,
        ?string $entityType = null
    ): ChatChannel {
        $channel = new ChatChannel();
        $channel->setType(ChatChannel::TYPE_DIRECT)
            ->setServer($server)
            ->setName($name);
        if ($parent) {
            $channel->setParent($parent);
        }
        if (!empty($from)) {
            $channel->setAvailableFrom($from);
        }
        if (!empty($to)) {
            $channel->setAvailableUntil($to);
        }
        if (!empty($entityType)) {
            $channel->setEntityType($entityType);
        }

        foreach ($users as $user) {
            $chatChannelUser = new ChatChannelUser();
            $chatChannelUser->setUser($user)
                ->setChannel($channel);
            $channel->addChatChannelUser($chatChannelUser);
        }

        $this->_em->persist($channel);
        $this->_em->flush();

        return $channel;
    }

    public function updateChannel(?ChatChannel $channel, string $name = 'default'): ChatChannel
    {
        $channel->setName($name);

        $this->_em->persist($channel);
        $this->_em->flush();

        return $channel;
    }

    public function addUserToDirectChannel(ChatChannel $channel, User $user)
    {
        if ($this->isUserInChannel($channel, $user)) {
            return $this->_em->getRepository(ChatChannelUser::class)->findOneBy(['user' => $user, 'channel' => $channel]);
        }

        $chatChannelUser = new ChatChannelUser();
        $chatChannelUser->setUser($user)
            ->setChannel($channel);

        $this->_em->persist($chatChannelUser);
        $this->_em->flush();

        return $chatChannelUser;
    }

    /**
     * @return ChatChannel|false
     */
    public function getChannel(
        ?int $channelId = null,
        ?int $parentId = null,
        ?string $type = null,
        ?string $name = null,
        ?\DateTime $from = null,
        ?\DateTime $to = null
    ) {
        if (empty($parentId) && empty($channelId)) {
            return false;
        }
        if (!empty($channelId)) {
            $channel = $this->find($channelId);
            if (!$channel) {
                return false;
            }

            return $channel;
        }

        $parent = $this->find($parentId);
        if (!$parent) {
            return false;
        }
        $channel = new ChatChannel();
        $channel->setParent($parent)
            ->setServer($parent->getServer());

        if (!empty($from)) {
            $channel->setAvailableFrom($from);
        }
        if (!empty($to)) {
            $channel->setAvailableUntil($to);
        }

        if (ChatChannel::TYPE_DIRECT === $parent->getType()) {
            $channel->setType(ChatChannel::TYPE_DIRECT);
        } else {
            if (!empty($type)) {
                $channel->setType($type);
            }
        }
        if (empty($name)) {
            $channel->setName($parent->getName());
        } else {
            $channel->setName($name);
        }

        $this->_em->persist($channel);
        $this->_em->flush();

        return $channel;
    }

    public function getChannelByType(ChatServer $server, string $type, bool $create = false): ?ChatChannel
    {
        $channel = $this->findOneBy([
            'server' => $server,
            'type' => $type
        ]);
        if (!$channel && $create) {
            $channel = new ChatChannel();
            $channel->setServer($server)
                ->setType($type)
                ->setName($type)
            ;

            $this->_em->persist($channel);
            $this->_em->flush();
        }

        return $channel;
    }

    public function getChannelByEntityType(
        ChatServer $server,
        string $type,
        ?int $entityId = null,
        ?string $entityType = null,
        bool $create = false
    ): ?ChatChannel {
        $channel = $this->findOneBy([
            'server' => $server,
            'type' => $type,
            'entityId' => $entityId,
            'entityType' => $entityType
        ]);
        if (!$channel && $create) {
            $channel = new ChatChannel();
            $channel->setServer($server)
                ->setType($type)
                ->setName($type)
                ->setEntityId($entityId)
                ->setEntityType($entityType);

            $this->_em->persist($channel);
            $this->_em->flush();
        }

        return $channel;
    }

    public function getMultipleChannelByEntityType(
        ChatServer $server,
        string $type,
        ?int $entityId = null,
        ?string $entityType = null
    ): ?array {
        $channels = $this->findBy([
            'server' => $server,
            'type' => $type,
            'entityId' => $entityId,
            'entityType' => $entityType
        ]);

        return $channels;
    }

    public function findChannelByUserAndParent($user, $chatChannel)
    {
        return $this->createQueryBuilder('cc')
            ->select('cc')
            ->join('cc.chatChannelUsers', 'ccu')
            ->where('ccu.user = :user')
            ->andWhere('cc.parent = :parent')
            ->setParameter('user', $user)
            ->setParameter('parent', $chatChannel)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function getDirectChatChannel(ChatChannel $parent, User $user1, User $user2, bool $create = true): ?ChatChannel
    {
        // Get current
        /** @var ChatChannel[] $channelsWithUser1 */
        $channelsWithUser1 = $this->createQueryBuilder('cc')
            ->select('cc')
            ->join('cc.chatChannelUsers', 'ccu')
            ->where('cc.parent =:parent')
            ->andWhere('ccu.user =:user1')
            ->setParameter('parent', $parent)
            ->setParameter('user1', $user1)
            ->getQuery()
            ->getResult()
        ;

        $user1AndUser2Channel = null;
        foreach ($channelsWithUser1 as $channel) {
            $users = $channel->getChatChannelUsers();
            if (2 != $users->count()) {
                continue;
            }// Not a direct channel between user and tutor
            if ($users->get(0)->getUser()->getId() === $user2->getId() || $users->get(1)->getUser()->getId() === $user2->getId()) {
                $user1AndUser2Channel = $channel;
                break;
            }
        }

        if (!$create) {
            return $user1AndUser2Channel;
        }

        if (!$user1AndUser2Channel) {
            $user1AndUser2Channel = new ChatChannel();
            $user1AndUser2Channel->setParent($parent)
                ->setType($parent->getType())
                ->setName($parent->getName())
                ->setServer($parent->getServer());

            $chatChannelUser1 = new ChatChannelUser();
            $chatChannelUser1->setUser($user1)
                ->setChannel($user1AndUser2Channel);
            $user1AndUser2Channel->addChatChannelUser($chatChannelUser1);

            $chatChannelUser2 = new ChatChannelUser();
            $chatChannelUser2->setUser($user2)
                ->setChannel($user1AndUser2Channel);
            $user1AndUser2Channel->addChatChannelUser($chatChannelUser2);

            $this->_em->persist($user1AndUser2Channel);
            $this->_em->flush();
        }

        return $user1AndUser2Channel;
    }

    public function addUser(int $channelId, int $userId): bool
    {
        $channel = $this->find($channelId);
        if (!$channel) {
            return false;
        }
        $user = $this->_em->getRepository(User::class)->find($userId);
        if (!$user) {
            return false;
        }
        $userChatChannel = $this->_em->getRepository(ChatChannelUser::class)->findOneBy([
            'user' => $user,
            'channel' => $channel
        ]);
        if (!$userChatChannel) {
            $userChatChannel = new ChatChannelUser();
            $userChatChannel->setUser($user)
                ->setChannel($channel)
                ->setCreatedAt(new \DateTimeImmutable());
            $this->_em->persist($userChatChannel);
            $this->_em->flush();
        }

        return true;
    }

    public function removeUser(int $channelId, int $userId): bool
    {
        $channel = $this->find($channelId);
        if (!$channel) {
            return false;
        }
        $user = $this->_em->getRepository(User::class)->find($userId);
        if (!$user) {
            return false;
        }
        $userChatChannel = $this->_em->getRepository(ChatChannelUser::class)->findOneBy([
            'user' => $user,
            'channel' => $channel
        ]);
        if ($userChatChannel) {
            $this->_em->remove($userChatChannel);
            $this->_em->flush();
        }

        return true;
    }
}
