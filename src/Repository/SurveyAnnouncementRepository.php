<?php

namespace App\Repository;

use App\Entity\Announcement;
use App\Entity\SurveyAnnouncement;
use App\Entity\Survey;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SurveyAnnouncement>
 *
 * @method SurveyAnnouncement|null find($id, $lockMode = null, $lockVersion = null)
 * @method SurveyAnnouncement|null findOneBy(array $criteria, array $orderBy = null)
 * @method SurveyAnnouncement[]    findAll()
 * @method SurveyAnnouncement[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SurveyAnnouncementRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SurveyAnnouncement::class);
    }

    public function add(SurveyAnnouncement $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(SurveyAnnouncement $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getAnnouncementsBySurvey(Survey $survey){
        return $this->createQueryBuilder('sa')
                    ->select('a.id', 'a.startAt', 'a.finishAt')
                    ->addSelect('c.id as courseId', 'c.code', 'c.name')
                    ->join('sa.announcement', 'a')
                    ->join('a.course', 'c')
                    ->where('sa.survey =:survey')
                    ->setParameter('survey', $survey)
                    ->getQuery()
                    ->getResult();
    }

    public function getAnnouncementAllInfoBySurvey(Survey $survey){
        return $this->createQueryBuilder('sa')
            ->join('sa.announcement', 'a')
            ->join('a.course', 'c')
            ->where('sa.survey =:survey')
            ->setParameter('survey', $survey)
            ->getQuery()
            ->getResult();
    }

}
