<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Announcement;
use App\Entity\AnnouncementManager;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementManager>
 *
 * @method AnnouncementManager|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementManager|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementManager[]    findAll()
 * @method AnnouncementManager[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementManagerRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementManager::class);
    }

    public function add(AnnouncementManager $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementManager $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findManagersByAnnouncement(Announcement $announcement): array
    {
        return $this->createQueryBuilder('am')
            ->select('u.id')
            ->join('am.manager', 'u')
            ->where('am.announcement = :announcement')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getResult();
    }

    public function removeAllByAnnouncement(Announcement $announcement): void
    {
        $this->createQueryBuilder('am')
            ->delete()
            ->where('am.announcement = :announcement')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->execute();
    }
}
