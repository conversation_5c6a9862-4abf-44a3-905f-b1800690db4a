<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Listener;

use App\Exception\InvalidDataException;
use App\Exception\TaskLimitExceededException;
use App\V2\Application\Log\Logger;
use App\V2\Domain\Announcement\Exception\AnnouncementGroupNotFoundException;
use App\V2\Domain\Announcement\Exception\AnnouncementManagerNotFoundException;
use App\V2\Domain\Announcement\Exception\AnnouncementNotFoundException;
use App\V2\Domain\Announcement\Exception\DeleteAnnouncementGroupUserCommandHandlerException;
use App\V2\Domain\Announcement\Exception\DeleteAnnouncementManagerException;
use App\V2\Domain\Announcement\Exception\GetAnnouncementManagerException;
use App\V2\Domain\Announcement\Exception\ManagerNotFoundException;
use App\V2\Domain\Announcement\Exception\PostAnnouncementGroupUserException;
use App\V2\Domain\Announcement\Exception\PutAnnouncementManagerException;
use App\V2\Domain\Announcement\Exception\UserNotAuthorizedException;
use App\V2\Domain\Billing\Exception\BillingDataNotFoundException;
use App\V2\Domain\Course\Creator\CourseCreatorNotFoundException;
use App\V2\Domain\Course\Creator\Exceptions\CreatorNotAuthorizedException;
use App\V2\Domain\Course\Creator\Exceptions\CreatorNotFoundException;
use App\V2\Domain\Course\Creator\Exceptions\GetCourseCreatorsException;
use App\V2\Domain\Course\Creator\Exceptions\PutCourseCreatorException;
use App\V2\Domain\Course\Exceptions\ChapterNotFoundException;
use App\V2\Domain\Course\Exceptions\CourseNotFoundException;
use App\V2\Domain\Course\Manager\Exceptions\GetManagerCoursesException;
use App\V2\Domain\LTI\Exceptions\GetLtiRegistrationQueryException;
use App\V2\Domain\LTI\Exceptions\LaunchLtiChapterQueryHandlerException;
use App\V2\Domain\LTI\Exceptions\LtiRegistrationNotFoundException;
use App\V2\Domain\LTI\Exceptions\LtiUnauthorizedException;
use App\V2\Domain\LTI\Exceptions\PostLtiDeploymentException;
use App\V2\Domain\LTI\Exceptions\PostLtiRegistrationException;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Purchase\Exception\PostCreatePurchaseException;
use App\V2\Domain\Purchase\Exception\PurchasableItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchaseNotFoundException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Criteria\InvalidSortException;
use App\V2\Domain\Shared\Resource\Exception\ResourceNotFoundException;
use App\V2\Domain\User\Exception\DeleteManagerFiltersCommandHandlerException;
use App\V2\Domain\User\Exception\DeleteUserFiltersCommandHandlerException;
use App\V2\Domain\User\Exception\GetManagerFiltersQueryHandlerException;
use App\V2\Domain\User\Exception\GetUserFiltersQueryHandlerException;
use App\V2\Domain\User\Exception\PostManagerFiltersCommandHandlerException;
use App\V2\Domain\User\Exception\PostUserFiltersCommandHandlerException;
use App\V2\Domain\User\Exception\UserForbiddenAction;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Infrastructure\Request\Exception\RequestAttributeExtractorException;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\Security\FirewallException;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\Exception\HttpExceptionInterface;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Routing\Exception\RouteNotFoundException;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;

class ExceptionListener implements EventSubscriberInterface
{
    public const array LOGGABLE_CODES = [
        Response::HTTP_INTERNAL_SERVER_ERROR,
        Response::HTTP_UNPROCESSABLE_ENTITY,
    ];

    public const array EXCLUDED_EXCEPTIONS = [
        AccessDeniedException::class,
    ];

    public function __construct(private readonly Logger $logger)
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::EXCEPTION => ['onKernelException', 100],
        ];
    }

    public function onKernelException(ExceptionEvent $event): void
    {
        $exception = $event->getThrowable();

        if (\in_array(\get_class($exception), self::EXCLUDED_EXCEPTIONS, true)) {
            return;
        }

        $responseCode = match (\get_class($exception)) {
            RequestAttributeExtractorException::class,
            UnauthorizedHttpException::class,
            LtiUnauthorizedException::class => Response::HTTP_UNAUTHORIZED,
            RouteNotFoundException::class,
            UserNotFoundException::class,
            AnnouncementNotFoundException::class,
            AnnouncementGroupNotFoundException::class,
            CourseNotFoundException::class,
            ChapterNotFoundException::class,
            LtiRegistrationNotFoundException::class,
            PurchaseNotFoundException::class,
            PurchasableItemNotFoundException::class,
            BillingDataNotFoundException::class => Response::HTTP_NOT_FOUND,
            InvalidDataException::class,
            InvalidSortException::class,
            ValidatorException::class,
            CriteriaException::class,
            InvalidPurchasableItemException::class => Response::HTTP_BAD_REQUEST,
            HttpExceptionInterface::class,
            TaskLimitExceededException::class,
            CreatorNotAuthorizedException::class,
            UserNotAuthorizedException::class,
            FirewallException::class,
            UserForbiddenAction::class => Response::HTTP_FORBIDDEN,
            PostLtiRegistrationException::class,
            GetLtiRegistrationQueryException::class,
            GetCourseCreatorsException::class,
            GetAnnouncementManagerException::class,
            GetManagerFiltersQueryHandlerException::class,
            CreatorNotFoundException::class,
            ManagerNotFoundException::class,
            PutCourseCreatorException::class,
            PutAnnouncementManagerException::class,
            AnnouncementManagerNotFoundException::class,
            CourseCreatorNotFoundException::class,
            PostLtiDeploymentException::class,
            LaunchLtiChapterQueryHandlerException::class,
            PostUserFiltersCommandHandlerException::class,
            PostManagerFiltersCommandHandlerException::class,
            GetUserFiltersQueryHandlerException::class,
            DeleteUserFiltersCommandHandlerException::class,
            DeleteManagerFiltersCommandHandlerException::class,
            ResourceNotFoundException::class,
            DeleteAnnouncementManagerException::class,
            PostCreatePurchaseException::class,
            DeleteAnnouncementGroupUserCommandHandlerException::class,
            PostAnnouncementGroupUserException::class,
            GetManagerCoursesException::class => Response::HTTP_UNPROCESSABLE_ENTITY,
            default => Response::HTTP_INTERNAL_SERVER_ERROR,
        };

        $message = $exception->getMessage();

        $responseContent = ApiResponseContent::createFromMessage($message);

        if ($exception instanceof ValidatorException) {
            $violations = [];
            foreach ($exception->getViolations() as $violation) {
                $violations[$violation->getPropertyPath()] = $violation->getMessage();
            }
            $responseContent->setMetadata(['violations' => $violations]);
        }

        if (\in_array($responseCode, self::LOGGABLE_CODES, true)) {
            $this->logger->error(
                message: $message,
                exception: $exception,
            );
        }

        $event->setResponse(
            new JsonResponse(
                $responseContent->toArray(),
                $responseCode
            )
        );
    }
}
