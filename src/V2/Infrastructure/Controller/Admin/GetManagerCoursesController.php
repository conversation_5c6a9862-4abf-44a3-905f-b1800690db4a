<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Query\Admin\GetManagerCourses;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Course\Manager\CourseManagerTransformer;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\Validator\Id\IdValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetManagerCoursesController extends QueryBusAccessor
{
    /**
     * @throws ValidatorException
     */
    public function __invoke(Request $request, int $userId): Response
    {
        IdValidator::validateId($userId);

        $courseManagerCollection = $this->ask(
            new GetManagerCourses(
                userId: new Id($userId)
            )
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData(
                CourseManagerTransformer::fromCollectionToArray($courseManagerCollection)
            )->toArray(),
            status: Response::HTTP_OK,
        );
    }
}
