<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Campus;

use App\V2\Application\Query\GetBillingData;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Billing\BillingDataTransformer;
use App\V2\Infrastructure\Request\Exception\RequestAttributeExtractorException;
use App\V2\Infrastructure\Request\RequestAttributeExtractor;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\Validator\Id\IdValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetBillingDataController extends QueryBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws RequestAttributeExtractorException
     */
    public function __invoke(int $userId, Request $request): Response
    {
        IdValidator::validateId($userId);

        $user = RequestAttributeExtractor::extractUser($request);

        $billingData = $this->ask(
            new GetBillingData(
                userId: new Id($userId),
                requestedBy: $user,
            )
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData(
                BillingDataTransformer::fromBillingDataToArray($billingData)
            )->toArray(),
            status: Response::HTTP_OK
        );
    }
}
