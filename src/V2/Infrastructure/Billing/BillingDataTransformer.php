<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Billing;

use App\V2\Domain\Billing\BillingData;

class BillingDataTransformer
{
    public static function fromBillingDataToArray(BillingData $billingData): array
    {
        return [
            'id' => $billingData->getId()->value(),
            'user_id' => $billingData->getUserId()->value(),
            'tin' => $billingData->getTin(),
            'first_name' => $billingData->getFirstName(),
            'last_name' => $billingData->getLastName(),
            'address' => $billingData->getAddress(),
            'postal_code' => $billingData->getPostalCode(),
            'city' => $billingData->getCity(),
            'country' => $billingData->getCountry(),
            'metadata' => $billingData->getMetadata(),
        ];
    }
}
