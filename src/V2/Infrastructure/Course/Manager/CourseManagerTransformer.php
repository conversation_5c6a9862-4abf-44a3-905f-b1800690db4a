<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Course\Manager;

use App\V2\Domain\Course\Manager\CourseManager;
use App\V2\Domain\Course\Manager\CourseManagerCollection;

class CourseManagerTransformer
{
    public static function fromCollectionToArray(CourseManagerCollection $collection): array
    {
        return array_map(
            fn (CourseManager $courseManager) => [
                'id' => $courseManager->getCourseId(),
            ],
            $collection->all()
        );
    }
}
