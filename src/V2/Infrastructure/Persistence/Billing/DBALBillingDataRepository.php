<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Billing;

use App\V2\Domain\Billing\BillingData;
use App\V2\Domain\Billing\BillingDataCollection;
use App\V2\Domain\Billing\BillingDataCriteria;
use App\V2\Domain\Billing\BillingDataRepository;
use App\V2\Domain\Billing\Exception\BillingDataNotFoundException;
use App\V2\Domain\Billing\Exception\BillingDataRepositoryException;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Persistence\DBALCommonCriteriaBuilder;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;

readonly class DBALBillingDataRepository implements BillingDataRepository
{
    public function __construct(
        private Connection $connection,
        private string $billingDataTableName,
    ) {
    }

    /**
     * @throws BillingDataRepositoryException
     * @throws CriteriaException
     * @throws InvalidUuidException
     */
    #[\Override]
    public function put(BillingData $billingData): void
    {
        try {
            $this->findOneBy(
                BillingDataCriteria::createById($billingData->getId())
            );

            $this->update($billingData);
        } catch (BillingDataNotFoundException) {
            $this->insert($billingData);
        }
    }

    /**
     * @throws BillingDataRepositoryException
     */
    private function insert(BillingData $billingData): void
    {
        try {
            $this->connection->insert(
                table: $this->billingDataTableName,
                data: $this->fromBillingDataToArray($billingData),
            );
        } catch (DBALException $e) {
            throw BillingDataRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws BillingDataRepositoryException
     */
    private function update(BillingData $billingData): void
    {
        try {
            $data = $this->fromBillingDataToArray($billingData);
            unset($data['id']); // Don't update the ID

            $this->connection->update(
                table: $this->billingDataTableName,
                data: $data,
                criteria: ['id' => $billingData->getId()->value()],
            );
        } catch (DBALException $e) {
            throw BillingDataRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws BillingDataNotFoundException
     * @throws BillingDataRepositoryException
     * @throws InvalidUuidException
     */
    #[\Override]
    public function findOneBy(BillingDataCriteria $criteria): ?BillingData
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->setMaxResults(1)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new BillingDataNotFoundException();
            }

            return $this->fromArrayToBillingData($result);
        } catch (DBALException $e) {
            throw BillingDataRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws BillingDataRepositoryException
     * @throws InvalidUuidException
     * @throws CollectionException
     */
    #[\Override]
    public function findBy(BillingDataCriteria $criteria): BillingDataCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new BillingDataCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToBillingData($values),
                    $result
                )
            );
        } catch (DBALException $e) {
            throw BillingDataRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws BillingDataRepositoryException
     * @throws BillingDataNotFoundException
     */
    #[\Override]
    public function delete(BillingData $billingData): void
    {
        try {
            $affectedRows = $this->connection->delete(
                table: $this->billingDataTableName,
                criteria: ['id' => $billingData->getId()->value()],
            );

            if (0 === $affectedRows) {
                throw new BillingDataNotFoundException();
            }
        } catch (BillingDataNotFoundException $e) {
            throw $e;
        } catch (DBALException $e) {
            throw BillingDataRepositoryException::fromPrevious($e);
        }
    }

    private function fromBillingDataToArray(BillingData $billingData): array
    {
        return [
            'id' => $billingData->getId()->value(),
            'user_id' => $billingData->getUserId()->value(),
            'first_name' => $billingData->getFirstName(),
            'last_name' => $billingData->getLastName(),
            'tin' => $billingData->getTin(),
            'address' => $billingData->getAddress(),
            'postal_code' => $billingData->getPostalCode(),
            'city' => $billingData->getCity(),
            'country' => $billingData->getCountry(),
            'metadata' => json_encode($billingData->getMetadata()),
        ];
    }

    /**
     * @throws InvalidUuidException
     */
    private function fromArrayToBillingData(array $values): BillingData
    {
        return new BillingData(
            id: new Uuid($values['id']),
            userId: new Id($values['user_id']),
            tin: $values['tin'],
            firstName: $values['first_name'],
            lastName: $values['last_name'],
            address: $values['address'],
            postalCode: $values['postal_code'],
            city: $values['city'],
            country: $values['country'],
            metadata: json_decode($values['metadata'], true) ?? [],
        );
    }

    private function getQueryBuilderByCriteria(BillingDataCriteria $criteria): QueryBuilder
    {
        $qb = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->billingDataTableName, 't');

        // Apply common criteria (ID, IDs, pagination, sorting)
        DBALCommonCriteriaBuilder::filterByCommonCriteria($criteria, $qb);

        // Filter by userId
        if (null !== $criteria->getUserId()) {
            $qb->andWhere('t.user_id = :user_id')
                ->setParameter('user_id', $criteria->getUserId()->value());
        }

        // Filter by tin
        if (null !== $criteria->getTin()) {
            $qb->andWhere('t.tin = :tin')
                ->setParameter('tin', $criteria->getTin());
        }

        return $qb;
    }
}
