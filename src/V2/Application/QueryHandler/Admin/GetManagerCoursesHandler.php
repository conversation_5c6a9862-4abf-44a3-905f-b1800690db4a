<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler\Admin;

use App\V2\Application\Query\Admin\GetManagerCourses;
use App\V2\Domain\Course\Manager\CourseManagerCollection;
use App\V2\Domain\Course\Manager\CourseManagerCriteria;
use App\V2\Domain\Course\Manager\CourseManagerRepository;
use App\V2\Domain\Course\Manager\Exceptions\GetManagerCoursesException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;

readonly class GetManagerCoursesHandler
{
    public function __construct(
        private UserRepository $userRepository,
        private CourseManagerRepository $courseManagerRepository,
    ) {
    }

    /**
     * @throws UserNotFoundException
     * @throws InfrastructureException
     * @throws GetManagerCoursesException
     * @throws CriteriaException
     */
    public function handle(GetManagerCourses $query): CourseManagerCollection
    {
        $user = $this->userRepository->findOneBy(
            UserCriteria::createById($query->getUserId())
        );

        if (!$user->isManager()) {
            throw GetManagerCoursesException::userIsNotAManager();
        }

        $criteria = CourseManagerCriteria::createEmpty()
            ->filterByUserId($query->getUserId());

        return $this->courseManagerRepository->findBy($criteria);
    }
}
