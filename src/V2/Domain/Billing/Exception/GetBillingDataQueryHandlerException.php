<?php

declare(strict_types=1);

namespace App\V2\Domain\Billing\Exception;

class GetBillingDataQueryHandlerException extends \Exception
{
    public static function fromPrevious(\Throwable $previous): self
    {
        return new self($previous->getMessage(), $previous->getCode(), $previous);
    }

    public static function userNotAllowed(): self
    {
        return new self('User not allowed to access this data');
    }
}
