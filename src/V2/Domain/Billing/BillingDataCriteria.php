<?php

declare(strict_types=1);

namespace App\V2\Domain\Billing;

use App\V2\Domain\Shared\Criteria\CriteriaWithUuid;
use App\V2\Domain\Shared\Id\Id;

class BillingDataCriteria extends CriteriaWithUuid
{
    private ?Id $userId = null;
    private ?string $tin = null;

    public function isEmpty(): bool
    {
        return parent::isEmpty()
            && null === $this->userId
            && null === $this->tin;
    }

    public function filterByUserId(Id $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getUserId(): ?Id
    {
        return $this->userId;
    }

    public function filterByTin(string $tin): self
    {
        $this->tin = $tin;

        return $this;
    }

    public function getTin(): ?string
    {
        return $this->tin;
    }
}
