<?php

declare(strict_types=1);

namespace App\V2\Domain\Billing;

use App\V2\Domain\Billing\Exception\BillingDataNotFoundException;
use App\V2\Domain\Billing\Exception\BillingDataRepositoryException;

interface BillingDataRepository
{
    /**
     * @throws BillingDataRepositoryException
     */
    public function put(BillingData $billingData): void;

    /**
     * @throws BillingDataRepositoryException
     * @throws BillingDataNotFoundException
     */
    public function findOneBy(BillingDataCriteria $criteria): ?BillingData;

    /**
     * @throws BillingDataRepositoryException
     */
    public function findBy(BillingDataCriteria $criteria): BillingDataCollection;

    /**
     * @throws BillingDataRepositoryException
     */
    public function delete(BillingData $billingData): void;
}
