<?php

declare(strict_types=1);

namespace App\V2\Domain\Course\Manager\Exceptions;

class GetManagerCoursesException extends \Exception
{
    public static function userIsNotAManager(): self
    {
        return new self('User is not a manager');
    }

    public static function fromPrevious(\Throwable $previous): self
    {
        return new self($previous->getMessage(), $previous->getCode(), $previous);
    }
}
