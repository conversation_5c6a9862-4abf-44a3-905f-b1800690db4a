<?php

declare(strict_types=1);

namespace App\V2\Domain\Course\Exceptions;

use App\V2\Domain\Shared\Exception\NotAuthorizedException;

class UserNotAuthorizedException extends NotAuthorizedException
{
    public static function userNotAllowedToManageCourseContent(string $email): self
    {
        return new self(
            \sprintf(
                'User %s is not allowed to manage the content of this course.',
                $email,
            )
        );
    }
}
