<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250827132832 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create billing_data table for V2 BillingData domain';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'SQL'
                CREATE TABLE billing_data (
                    id CHAR(36) NOT NULL,
                    user_id int NOT NULL,
                    tin VARCHAR(255) NOT NULL,
                    first_name VARCHAR(255) NOT NULL,
                    last_name VA<PERSON>HAR(255) NOT NULL,
                    address TEXT NOT NULL,
                    postal_code VARCHAR(20) NOT NULL,
                    city VARCHAR(255) NOT NULL,
                    country VARCHAR(255) NOT NULL,
                    metadata JSON,
                    PRIMARY KEY(id),
                    INDEX idx_billing_data_user_id (user_id),
                    INDEX idx_billing_data_tin (tin)
                ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
            SQL
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE billing_data');
    }
}
