Issue: Closes #769 — Resolve "Create GetManagerCourses endpoint"
Role: AI Reviewer

Resumen:
Este MR introduce el endpoint GET /api/v2/admin/managers/{userId}/courses para obtener los cursos gestionados por un manager. Incluye:
- OpenAPI actualizado con la nueva ruta.
- <PERSON><PERSON> + <PERSON><PERSON> (GetManagerCourses, GetManagerCoursesHandler) con validación de rol de manager.
- Excepción de dominio GetManagerCoursesException mapeada a 422.
- Controlador GetManagerCoursesController + ruta.
- Transformer para salida mínima (id del curso).
- Tests unitarios de Query, Handler y Transformer, y tests funcionales del endpoint.

Buenas prácticas adoptadas:
- [x] Segregación por capas (Application/Domain/Infrastructure) coherente con V2 y DDD.
- [x] Uso de Criteria y Repositorios para acceso a datos.
- [x] Validación de ID en controlador con IdValidator.
- [x] Manejo explícito de errores (UserNotFound → 404 por listener, user no manager → 422).
- [x] Respuesta homogeneizada mediante ApiResponseContent y transformer dedicado.
- [x] Cobertura de tests razonable (unitarios + funcionales) y pipeline en verde.

Problemas críticos encontrados:
- [ ] OpenAPI: los ejemplos incluidos para el 200 están correctos, pero se ha añadido contenido de errores 400/401/403/404/422 parcialmente inconsistente con las respuestas reales del proyecto (p. ej., estructura {error|message}). El comentario en la MR indica “Esto no es un ejemplo válido”; concuerdo: habría que alinear ejemplos con el formato real (ApiResponseContent) o referenciar componentes existentes para errores, como ya se hace en otras rutas.
- [ ] Tests funcionales: falta cubrir 403 Forbidden (usuario autenticado sin permisos adecuados) y validar el 400 por Id no válido en la URI, según comentario previo. Actualmente solo hay 401, 404, 422, y casos OK. Deberían añadirse para cumplir criterios de seguridad y contrato.

Sugerencias de mejora:
- [ ] Tests de Handler: se están mockeando instancias de User. En el proyecto solemos preferir usar Mother para entidades/aggregates no inyectados (p. ej., UserMother) como apuntan los comentarios. Esto simplifica expectativas y mejora el realismo de los tests. También podéis parametrizar tests del Transformer con un data provider para evitar duplicidad, como se sugiere en los comentarios.
- [ ] En GetManagerCoursesHandler la anotación de throws tiene un pequeño typo: “@throws GetManagerCoursesException|” seguido de salto de línea; eliminar el pipe sobrante para mantener consistencia (comentario existente “Quitar | final” es apropiado).
- [ ] Consistencia OpenAPI:
  - Reutilizar componentes comunes para errores (components/responses o schemas ya presentes) para 401/403/404/422 y describir el 200 con un schema reusable si ya existe para listados con data[].
  - Revisar que el summary/description mencionen los roles exactos admitidos y que coincidan con la lógica (ROLE_MANAGER o ROLE_MANAGER_EDITOR) y con el control de seguridad de la ruta si aplica a nivel de firewall/attributes.
- [ ] Transformer: ahora devuelve solo id. ¿Se requiere exponer más campos? Si en el futuro hicieran falta nombres/títulos, convendría decidir si este endpoint es solo IDs (rápido) o si habrá un endpoint que resuelva detalles de cursos. Documentarlo en la descripción del endpoint ayuda a evitar malentendidos.
- [ ] Listener: el mapeo a 422 para GetManagerCoursesException es correcto. Verificar que UserNotFoundException efectivamente mapea a 404 en este listener (parece que sí, pero asegurar que no haya colisiones en prioridades).

Comentarios adicionales:
- Naming y rutas siguen la convención actual (/api/v2/admin/...). La ayuda auxiliar AdminManagerEndpoints está alineada.
- La Query en sí es trivial; el test de la Query puede considerarse prescindible en V2 salvo que el proyecto lo requiera por convención (el comentario de que “no hace falta testear por testear” es razonable). Mantener la consistencia con el resto del código base: si no se testean Queries simples en general, eliminarlo; si sí, mantenerlo.
- Cobertura: faltaría test funcional para el caso de usuario autenticado sin permisos (403), y un test de validación de path param inválido (IdValidator → 400/422 según convención de proyecto). Los comentarios previos ya lo han pedido; estoy de acuerdo.

Resumen de la revisión:
El MR está bien orientado y cumple con la arquitectura V2. La lógica principal es correcta y la experiencia de API es consistente con el resto del sistema. Quedan ajustes menores de calidad: alinear OpenAPI con respuestas reales, completar cobertura de casos de error (403 y validación de ID), y pulir tests/unit con Mothers y data providers. No he identificado problemas de seguridad o rendimiento relevantes más allá de la necesidad de asegurar el 403.

Acciones recomendadas:
- [ ] Añadir tests funcionales para 403 (usuario autenticado sin ROLE_MANAGER/ROLE_MANAGER_EDITOR) y para ID inválido en la ruta.
- [ ] Corregir la anotación @throws en el handler (retirar el pipe final) y considerar usar UserMother en los tests del handler.
- [ ] Refactorizar tests del transformer con data provider y eliminar redundancias.
- [ ] Ajustar OpenAPI: reutilizar componentes de error y validar ejemplos; si el proyecto tiene un schema estándar para ApiResponseContent, referenciarlo.

Veredicto: Needs changes menores antes de merge.