require('dotenv-flow').config();
const inquirer = require('inquirer');
const inquirerPrompt = require('inquirer-autocomplete-prompt');
inquirer.registerPrompt('autocomplete', inquirerPrompt);
const {getLatestTag, getNextTag, runNodeCommand} = require('./utils');



const updateTag = async () => {
    const latestTag = getLatestTag();
    const {version} = await inquirer.prompt([
        {
            type: 'autocomplete',
            name: 'version',
            message: 'Seleccione el tipo de versión',
            source: async (answers, input) => {
                const versions = ['patch', 'minor', 'major'];
                return versions.filter(version => version.includes(input || ''));
            }
        }
    ]);

    const nextTag = getNextTag(latestTag, version);
    // create git tag
    await runNodeCommand(`git tag -a ${nextTag} -m "Release ${nextTag}"`);
    console.log(`Tag ${nextTag} creado`);
};

updateTag().then(() => {
});
