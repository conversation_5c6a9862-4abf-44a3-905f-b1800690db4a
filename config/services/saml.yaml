parameters:
  saml.enabled: "%env(bool:SAML_ENABLED)%"
  saml.destination: "%env(SAML_AUTH_DESTINATION)%"    # Login URL
  saml.issuer: "%env(SAML_AUTH_ISSUER)%"              # Azure AD / Enterprise Applications / App[Application ID]
  saml.token_ttl: "%env(SAML_AUTH_TOKEN_TTL)%"        # ttl in seconds
  saml.attributes.namespace: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims"

  ## If signature validation is enabled, is required to have the public key of the certificate
  saml.validate_signature: "%env(bool:SAML_VALIDATE_SIGNATURE)%"
  saml.certificate_location: '/certs/'
  saml.certificate_file: "/certs/easylearning.cer"  # It can be in another location

  #####################################################
  ### Relation entity <-> attributeName or          ###
  ### saml.roles:attribute is set without namespace ###
  #####################################################

  saml.user.primaryKey: email

  # All these fields are required, the attribute name cannot be changed
  saml.user.default_and_required:
    email: emailaddress
    firstName: givenname
    lastName: surname
    code: employeeid

  saml.user.required_attributes:
    email: emailaddress
    firstName: givenname
    lastName: surname
    code: employeeid
    teamManagerEmail: manager

  saml.user.filters:
    attribute1:
      - 1 # Category id 1
      - 2 # Category id 2
      - 3 # Category id 3
  saml.user.role.enable: "%env(bool:SAML_ROLE_USER_ENABLED)%"
  saml.user.role.attribute: "%env(SAML_ROLE_ATTRIBUTE)%"
  saml.user.role.roles:
    ROLE_ADMIN:
      - Admin TI
  saml.data.extra:
    UserExtra:
      birthdate: Birthdate
      gender: Gender

#  saml.user:
#    primaryKey: email
#    relations:
#      email: emailaddress
#      firstName: givenname
#      lastName: surname
#      code: employeeid
#      teamManagerEmail: manager

  #####################################################################
  ### Define what attributes are filters in relation with the user  ###
  ### attributeName: categoryId                                     ###
  #####################################################################
#  saml.filters:
#    department: 1 # Área
#    jobtitle: 2
#    companyname: 3 # Empresa
#    office: 2 # Centro de Trabajo
#    State: 5 # Territorio
#    Title: 4 # Categoría Profesional

  #####################################################
  ### Set relations between User and other entities ###
  ###  user:                                        ###
  ###    className: "App\\Entity\\User" # Required  ###
  ###    primaryKey: email              # Required  ###
  ###    attribute: department          # Required  ###
  ###    property: teamManager          # Required  ###
  #####################################################
#  saml.user.extra:
#    user:
#      className: "App\\Entity\\User"
#      primaryKey: email
#      attribute: manager
#      property: teamManager

  #######################################################
  ### Read roles and set values based on conditions   ###
  ### If the role is required to be saved in database ###
  ### make the relation Entity <-> AttributeName in   ###
  ### section: saml.user                              ###
  ### Replace current values for the actual values    ###
  ### Is required to use the real role name declared  ###
  ### in the User entity                              ###
  ### Note: Roles definition configured by client     ###
  ### Current data is used as an example              ###
  #######################################################
#  saml.roles:
#    enabled: "%env(bool:SAML_ROLE_USER_ENABLED)%"
#    attribute: "%env(SAML_ROLE_ATTRIBUTE)%"
#    relations:
#      ROLE_ADMIN:
#        - Admin TI
#      ROLE_MANAGER:
#        - Manager
