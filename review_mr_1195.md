Issue: Closes #754 — Resolve "Update the purchase invoicing data"
Role: AI Reviewer

Resumen:
Este MR introduce el endpoint PATCH /purchases/{purchaseId}/billing-data en Campus (OpenAPI actualizado) para actualizar los datos de facturación de una compra concreta, con la opción de guardar esos datos como predeterminados del usuario. Incluye:
- Definición OpenAPI con validaciones de formato y campos requeridos.
- Nuevo Command y CommandHandler (PatchPurchaseBillingDataCommand / Handler).
- Controlador Campus PatchPurchaseBillingDataController que delega en el CommandBus.
- Extensiones en Purchase (billingData) y fábrica/repositorio relacionados.
- Validaciones (PurchaseBillingDataValidator) y tests funcionales para flujos positivos y errores (propiedad de la compra, UUID inválido, errores de validación, save_as_default, etc.).

Buenas prácticas adoptadas:
- [x] Enfoque CQRS/DDD: uso de Command + Handler y controlador delgado.
- [x] Cobertura de OpenAPI coherente con los contratos expuestos.
- [x] Tests funcionales que validan caminos felices y de error (incluye helper de endpoint).
- [x] Control de autorización: usuario propietario vs. admin (se corrige una comprobación en un commit posterior).
- [x] Manejo explícito de save_as_default y creación/actualización de BillingData centralizada en el Handler.

Problemas críticos encontrados:
- [ ] Falta confirmación de consistencia entre la validación OpenAPI y la validación del dominio. Por ejemplo, country acepta 2–3 letras mayúsculas (ISO2/ISO3) en OpenAPI; asegurar que el validador de dominio/aplicación aplica la misma regla (o documentar la razón de divergencia si solo se admite ISO2 en backend).
- [ ] Persistencia y uso efectivo de Purchase.billingData: revisar que el campo se persista realmente en BD y que los repositorios/ORM mapping estén actualizados (no se aprecia en el diff si se añadió la columna o embeddable/relación y sus migraciones). Si no existe migración asociada, esto provocará errores en producción.
- [ ] Idempotencia y concurrencia: al actualizar billingData de una compra y, opcionalmente, los datos por defecto del usuario, no se observa manejo de condiciones de carrera. Probablemente no sea crítico ahora, pero conviene, al menos, añadir una nota o prueba sobre idempotencia: repetir el PATCH con el mismo payload debería dejar el estado estable sin efectos secundarios.

Sugerencias de mejora:
- [ ] Validación de longitud y normalización: asegurar trim/normalización (espacios, casing) antes de validar maxLength; en tests añadir casos con espacios finales/iniciales.
- [ ] Respuestas de error: documentar en OpenAPI los distintos códigos de error que ya se prueban (403 por no ser propietario, 404 si la compra no existe, 422 para validaciones), incluyendo ejemplos, para que clientes sepan diferenciarlos.
- [ ] Telemetría/Logging: añadir logs estructurados en el Handler cuando se actualiza billingData y cuando se guarda como default, para trazabilidad.
- [ ] Seguridad de metadatos: metadata es un objeto abierto. Considerar lista blanca de claves permitidas o tamaño máximo total; documentar límites para evitar payloads grandes.
- [ ] Tests adicionales: añadir un test que verifique que un admin puede actualizar datos de cualquier compra; y otro en el que el usuario A no pueda modificar la compra del usuario B (si no está ya cubierto en los tests actuales).
- [ ] Naming/consistencia: en los commit messages aparece tanto Campus /campus/purchase-billing-data como /purchases/{purchaseId}/billing-data. Asegurar que solo queda la ruta final y que no existe endpoint duplicado/antiguo. Los helpers de endpoints (CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint) parecen correctos; revisar que no queden restos del path antiguo.
- [ ] Tipado estricto: si se permite metadata, validar que sea array asociativo con claves string; rechazar arrays numéricos.
- [ ] Guardado por defecto: si save_as_default=true y el usuario no tiene BillingData existente, ya se añadió un fix. Añadir test que cubra también la actualización cuando sí existe y cuando los datos son iguales (no-op) para evitar escrituras innecesarias.

Comentarios adicionales:
- OpenAPI: el patrón ^[A-Z]{2,3}$ para country está bien, pero si internamente solo se acepta ISO2, limítalo a 2 letras. Si aceptas ambos, documenta claramente cuál se guardará y cómo se expone luego.
- Asegurarse de que la capa de autorización se centralice (por ejemplo, un PurchaseOwnershipPolicy) para evitar futuras inconsistencias.

Resumen de la revisión:
La implementación está bien orientada (DDD/CQRS), con buenas pruebas funcionales y documentación OpenAPI. Hay que verificar detenidamente la persistencia de la nueva propiedad billingData en la entidad Purchase (migraciones/mapping) y alinear las reglas de validación entre OpenAPI y backend, además de pulir detalles de robustez (normalización, límites de metadata) y completar algunos tests y documentación de errores.

Acciones recomendadas:
- [ ] Confirmar/añadir migración y mapping para Purchase.billingData y cualquier relación asociada.
- [ ] Alinear validaciones de country entre OpenAPI y validador de dominio.
- [ ] Documentar en OpenAPI respuestas 403/404/422 con ejemplos.
- [ ] Añadir tests: admin actualiza compra de otro; usuario no propietario recibe 403; save_as_default no duplica/reescribe si no cambia; idempotencia.
- [ ] Revisar límites de metadata y tipo estricto de claves.
- [ ] Añadir logs en el Handler para cambios relevantes.

Veredicto:
Needs changes menores. La base es sólida, pero antes de merge propondría cerrar los puntos de validación/persistencia/documentación indicados para evitar regresiones en producción.