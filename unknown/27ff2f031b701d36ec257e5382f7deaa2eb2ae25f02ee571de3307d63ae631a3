<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Subscription;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\Subscription\SubscriptionMother;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Subscription\Subscription;
use App\V2\Domain\Subscription\SubscriptionCollection;

class SubscriptionCollectionTest extends CollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new SubscriptionCollection($items);
    }

    protected function getExpectedType(): string
    {
        return Subscription::class;
    }

    /**
     * @throws InvalidUuidException
     */
    protected function getItem(): object
    {
        return SubscriptionMother::create();
    }
}
