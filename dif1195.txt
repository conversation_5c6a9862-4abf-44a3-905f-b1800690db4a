--- openapi/v2/campus/openapi.yaml
+++ openapi/v2/campus/openapi.yaml
@@ -286,6 +286,174 @@ paths:
               example:
                 message: "Internal server error"
 
+  /purchases/{purchaseId}/billing-data:
+    patch:
+      tags:
+        - Purchases
+      summary: Update purchase billing data
+      description: |
+        Updates the billing data for a specific purchase. The authenticated user must own the purchase or have admin privileges.
+
+        **Features:**
+        - Updates billing information for the purchase
+        - Optionally saves the billing data as default for the user (save_as_default: true)
+        - Validates all required billing fields
+        - Supports metadata for additional custom fields
+
+        **Access Control:**
+        - Users can only update billing data for their own purchases
+        - Admin users can update billing data for any purchase
+      operationId: patchPurchaseBillingData
+      parameters:
+        - name: purchaseId
+          in: path
+          required: true
+          schema:
+            $ref: '#/components/schemas/Uuid'
+          description: The unique identifier of the purchase
+      requestBody:
+        required: true
+        content:
+          application/json:
+            schema:
+              type: object
+              required:
+                - tin
+                - first_name
+                - last_name
+                - address
+                - postal_code
+                - city
+                - country
+              properties:
+                tin:
+                  type: string
+                  maxLength: 50
+                  description: Tax identification number
+                  example: "12345678A"
+                first_name:
+                  type: string
+                  maxLength: 100
+                  description: First name
+                  example: "John"
+                last_name:
+                  type: string
+                  maxLength: 100
+                  description: Last name
+                  example: "Doe"
+                address:
+                  type: string
+                  maxLength: 255
+                  description: Street address
+                  example: "123 Main Street"
+                postal_code:
+                  type: string
+                  maxLength: 20
+                  description: Postal code
+                  example: "12345"
+                city:
+                  type: string
+                  maxLength: 100
+                  description: City
+                  example: "Madrid"
+                country:
+                  type: string
+                  minLength: 2
+                  maxLength: 3
+                  pattern: "^[A-Z]{2,3}$"
+                  description: Country code (ISO 2-3 letter format)
+                  example: "ES"
+                metadata:
+                  type: object
+                  additionalProperties: true
+                  description: Additional metadata as key-value pairs
+                  example:
+                    company: "ACME Corp"
+                    department: "IT"
+                save_as_default:
+                  type: boolean
+                  description: Whether to save this billing data as default for the user
+                  example: false
+            example:
+              tin: "12345678A"
+              first_name: "John"
+              last_name: "Doe"
+              address: "123 Main Street"
+              postal_code: "12345"
+              city: "Madrid"
+              country: "ES"
+              metadata:
+                company: "ACME Corp"
+              save_as_default: false
+      responses:
+        '200':
+          description: Purchase billing data updated successfully
+          content:
+            application/json:
+              schema:
+                type: object
+                properties:
+                  message:
+                    type: string
+                    example: "Purchase billing data updated successfully"
+        '400':
+          description: Invalid request data
+          content:
+            application/json:
+              schema:
+                $ref: '#/components/schemas/ValidationError'
+              examples:
+                invalid_uuid:
+                  summary: Invalid UUID format
+                  value:
+                    message: "Validation failed"
+                    metadata:
+                      violations:
+                        - field: "purchaseId"
+                          message: "Invalid UUID format"
+                invalid_billing_data:
+                  summary: Invalid billing data
+                  value:
+                    message: "Validation failed"
+                    metadata:
+                      violations:
+                        - field: "tin"
+                          message: "This field is required"
+                        - field: "country"
+                          message: "Country must be a valid ISO country code (2-3 uppercase letters)"
+        '401':
+          description: Unauthorized - Authentication required
+          content:
+            application/json:
+              schema:
+                $ref: '#/components/schemas/Error'
+              example:
+                message: "Authentication required"
+        '403':
+          description: Forbidden - User cannot access this purchase
+          content:
+            application/json:
+              schema:
+                $ref: '#/components/schemas/Error'
+              example:
+                message: "Access denied to this purchase"
+        '404':
+          description: Purchase not found
+          content:
+            application/json:
+              schema:
+                $ref: '#/components/schemas/Error'
+              example:
+                message: "Purchase not found"
+        '500':
+          description: Internal server error
+          content:
+            application/json:
+              schema:
+                $ref: '#/components/schemas/Error'
+              example:
+                message: "Internal server error"
+
   /users/{userId}/billing-data:
     get:
       tags:
--- src/Migrations/Version20250828000000.php
+++ src/Migrations/Version20250828000000.php
@@ -0,0 +1,26 @@
+<?php
+
+declare(strict_types=1);
+
+namespace DoctrineMigrations;
+
+use Doctrine\DBAL\Schema\Schema;
+use Doctrine\Migrations\AbstractMigration;
+
+final class Version20250828000000 extends AbstractMigration
+{
+    public function getDescription(): string
+    {
+        return 'Add billing_data JSON column to purchase table';
+    }
+
+    public function up(Schema $schema): void
+    {
+        $this->addSql('ALTER TABLE purchase ADD COLUMN billing_data JSON NULL');
+    }
+
+    public function down(Schema $schema): void
+    {
+        $this->addSql('ALTER TABLE purchase DROP COLUMN billing_data');
+    }
+}
--- src/V2/Application/Command/PatchPurchaseBillingDataCommand.php
+++ src/V2/Application/Command/PatchPurchaseBillingDataCommand.php
@@ -0,0 +1,41 @@
+<?php
+
+declare(strict_types=1);
+
+namespace App\V2\Application\Command;
+
+use App\V2\Domain\Bus\Command;
+use App\V2\Domain\Purchase\PurchaseBillingData;
+use App\V2\Domain\Shared\Id\Id;
+use App\V2\Domain\Shared\Uuid\Uuid;
+
+readonly class PatchPurchaseBillingDataCommand implements Command
+{
+    public function __construct(
+        private Uuid $purchaseId,
+        private Id $userId,
+        private PurchaseBillingData $purchaseBillingData,
+        private bool $saveAsDefault,
+    ) {
+    }
+
+    public function getPurchaseId(): Uuid
+    {
+        return $this->purchaseId;
+    }
+
+    public function getUserId(): Id
+    {
+        return $this->userId;
+    }
+
+    public function getPurchaseBillingData(): PurchaseBillingData
+    {
+        return $this->purchaseBillingData;
+    }
+
+    public function isSaveAsDefault(): bool
+    {
+        return $this->saveAsDefault;
+    }
+}
--- src/V2/Application/CommandHandler/PatchPurchaseBillingDataCommandHandler.php
+++ src/V2/Application/CommandHandler/PatchPurchaseBillingDataCommandHandler.php
@@ -0,0 +1,84 @@
+<?php
+
+declare(strict_types=1);
+
+namespace App\V2\Application\CommandHandler;
+
+use App\V2\Application\Command\PatchPurchaseBillingDataCommand;
+use App\V2\Application\Purchase\PurchaseFactory;
+use App\V2\Domain\Billing\BillingData;
+use App\V2\Domain\Billing\BillingDataCriteria;
+use App\V2\Domain\Billing\BillingDataRepository;
+use App\V2\Domain\Billing\Exception\BillingDataNotFoundException;
+use App\V2\Domain\Billing\Exception\BillingDataRepositoryException;
+use App\V2\Domain\Purchase\Exception\PurchaseNotAllowedException;
+use App\V2\Domain\Purchase\Exception\PurchaseNotFoundException;
+use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
+use App\V2\Domain\Purchase\PurchaseCriteria;
+use App\V2\Domain\Purchase\PurchaseRepository;
+use App\V2\Domain\Shared\Criteria\CriteriaException;
+use App\V2\Domain\Shared\Uuid\UuidGenerator;
+
+readonly class PatchPurchaseBillingDataCommandHandler
+{
+    public function __construct(
+        private PurchaseRepository $purchaseRepository,
+        private BillingDataRepository $billingDataRepository,
+        private PurchaseFactory $purchaseFactory,
+        private UuidGenerator $uuidGenerator,
+    ) {
+    }
+
+    /**
+     * @throws BillingDataRepositoryException
+     * @throws PurchaseRepositoryException
+     * @throws BillingDataNotFoundException
+     * @throws PurchaseNotFoundException
+     * @throws CriteriaException
+     * @throws PurchaseNotAllowedException
+     */
+    public function handle(PatchPurchaseBillingDataCommand $command): void
+    {
+        $foundPurchase = $this->purchaseRepository->findOneBy(
+            PurchaseCriteria::createById($command->getPurchaseId())
+        );
+
+        if (!$foundPurchase->getUserId()->equals($command->getUserId())) {
+            throw PurchaseNotAllowedException::userNotAllowed();
+        }
+
+        $purchase = $this->purchaseFactory->updateWithBillingData(
+            $foundPurchase,
+            $command->getPurchaseBillingData()
+        );
+
+        $this->purchaseRepository->put($purchase);
+
+        if ($command->isSaveAsDefault()) {
+            try {
+                $foundBillingData = $this->billingDataRepository->findOneBy(
+                    BillingDataCriteria::createEmpty()
+                        ->filterByUserId($command->getUserId())
+                );
+                $billingDataId = $foundBillingData->getId();
+            } catch (BillingDataNotFoundException) {
+                $billingDataId = $this->uuidGenerator->generate();
+            }
+
+            $billingData = new BillingData(
+                id: $billingDataId,
+                userId: $command->getUserId(),
+                tin: $command->getPurchaseBillingData()->getTin(),
+                firstName: $command->getPurchaseBillingData()->getFirstName(),
+                lastName: $command->getPurchaseBillingData()->getLastName(),
+                address: $command->getPurchaseBillingData()->getAddress(),
+                postalCode: $command->getPurchaseBillingData()->getPostalCode(),
+                city: $command->getPurchaseBillingData()->getCity(),
+                country: $command->getPurchaseBillingData()->getCountry(),
+                metadata: $command->getPurchaseBillingData()->getMetadata(),
+            );
+
+            $this->billingDataRepository->put($billingData);
+        }
+    }
+}
--- src/V2/Application/Purchase/PurchaseFactory.php
+++ src/V2/Application/Purchase/PurchaseFactory.php
@@ -7,6 +7,7 @@ namespace App\V2\Application\Purchase;
 use App\V2\Domain\Purchase\PurchasableItem;
 use App\V2\Domain\Purchase\PurchasableItemCollection;
 use App\V2\Domain\Purchase\Purchase;
+use App\V2\Domain\Purchase\PurchaseBillingData;
 use App\V2\Domain\Purchase\PurchaseItem;
 use App\V2\Domain\Purchase\PurchaseItemCollection;
 use App\V2\Domain\Purchase\PurchaseStatus;
@@ -87,4 +88,22 @@ readonly class PurchaseFactory
             price: $purchasableItem->getPrice(),
         );
     }
+
+    public function updateWithBillingData(
+        Purchase $existingPurchase,
+        PurchaseBillingData $billingData,
+    ): Purchase {
+        return new Purchase(
+            id: $existingPurchase->getId(),
+            userId: $existingPurchase->getUserId(),
+            status: $existingPurchase->getStatus(),
+            amount: $existingPurchase->getAmount(),
+            taxRate: $existingPurchase->getTaxRate(),
+            taxAmount: $existingPurchase->getTaxAmount(),
+            createdAt: $existingPurchase->getCreatedAt(),
+            updatedAt: new \DateTimeImmutable(),
+            deletedAt: $existingPurchase->getDeletedAt(),
+            billingData: $billingData,
+        );
+    }
 }
--- src/V2/Domain/Purchase/Exception/PurchaseNotAllowedException.php
+++ src/V2/Domain/Purchase/Exception/PurchaseNotAllowedException.php
@@ -0,0 +1,18 @@
+<?php
+
+declare(strict_types=1);
+
+namespace App\V2\Domain\Purchase\Exception;
+
+class PurchaseNotAllowedException extends \Exception
+{
+    public static function fromPrevious(\Throwable $previous): self
+    {
+        return new self($previous->getMessage(), $previous->getCode(), $previous);
+    }
+
+    public static function userNotAllowed(): self
+    {
+        return new self('User not allowed to update purchase billing data');
+    }
+}
--- src/V2/Domain/Purchase/Purchase.php
+++ src/V2/Domain/Purchase/Purchase.php
@@ -29,6 +29,7 @@ class Purchase extends LifeCycleEntity
         \DateTimeImmutable $createdAt,
         ?\DateTimeImmutable $updatedAt = null,
         ?\DateTimeImmutable $deletedAt = null,
+        private readonly ?PurchaseBillingData $billingData = null,
     ) {
         parent::__construct(
             id: $id,
@@ -63,6 +64,11 @@ class Purchase extends LifeCycleEntity
         return $this->taxAmount;
     }
 
+    public function getBillingData(): ?PurchaseBillingData
+    {
+        return $this->billingData;
+    }
+
     public function getPurchaseItems(): ?PurchaseItemCollection
     {
         return $this->purchaseItems;
--- src/V2/Domain/Purchase/PurchaseBillingData.php
+++ src/V2/Domain/Purchase/PurchaseBillingData.php
@@ -0,0 +1,60 @@
+<?php
+
+declare(strict_types=1);
+
+namespace App\V2\Domain\Purchase;
+
+readonly class PurchaseBillingData
+{
+    public function __construct(
+        public string $tin,
+        public string $firstName,
+        public string $lastName,
+        public string $address,
+        public string $postalCode,
+        public string $city,
+        public string $country,
+        public array $metadata,
+    ) {
+    }
+
+    public function getTin(): string
+    {
+        return $this->tin;
+    }
+
+    public function getFirstName(): string
+    {
+        return $this->firstName;
+    }
+
+    public function getLastName(): string
+    {
+        return $this->lastName;
+    }
+
+    public function getAddress(): string
+    {
+        return $this->address;
+    }
+
+    public function getPostalCode(): string
+    {
+        return $this->postalCode;
+    }
+
+    public function getCity(): string
+    {
+        return $this->city;
+    }
+
+    public function getCountry(): string
+    {
+        return $this->country;
+    }
+
+    public function getMetadata(): array
+    {
+        return $this->metadata;
+    }
+}
--- src/V2/Infrastructure/Controller/Campus/PatchPurchaseBillingDataController.php
+++ src/V2/Infrastructure/Controller/Campus/PatchPurchaseBillingDataController.php
@@ -0,0 +1,53 @@
+<?php
+
+declare(strict_types=1);
+
+namespace App\V2\Infrastructure\Controller\Campus;
+
+use App\V2\Application\Command\PatchPurchaseBillingDataCommand;
+use App\V2\Domain\Shared\Id\Id;
+use App\V2\Domain\Shared\Uuid\InvalidUuidException;
+use App\V2\Domain\Shared\Uuid\Uuid;
+use App\V2\Infrastructure\Bus\CommandBusAccessor;
+use App\V2\Infrastructure\Purchase\PurchaseTransformer;
+use App\V2\Infrastructure\Request\Exception\RequestAttributeExtractorException;
+use App\V2\Infrastructure\Request\RequestAttributeExtractor;
+use App\V2\Infrastructure\Response\ApiResponseContent;
+use App\V2\Infrastructure\Validator\Campus\PurchaseBillingDataValidator;
+use App\V2\Infrastructure\Validator\Uuid\UuidValidator;
+use App\V2\Infrastructure\Validator\ValidatorException;
+use Symfony\Component\HttpFoundation\JsonResponse;
+use Symfony\Component\HttpFoundation\Request;
+use Symfony\Component\HttpFoundation\Response;
+
+class PatchPurchaseBillingDataController extends CommandBusAccessor
+{
+    /**
+     * @throws InvalidUuidException
+     * @throws RequestAttributeExtractorException
+     * @throws ValidatorException
+     */
+    public function __invoke(string $purchaseId, Request $request): Response
+    {
+        UuidValidator::validateUuid($purchaseId);
+
+        $content = json_decode($request->getContent(), true);
+        PurchaseBillingDataValidator::validatePurchaseBillingData($content ?? []);
+
+        $this->execute(
+            new PatchPurchaseBillingDataCommand(
+                purchaseId: new Uuid($purchaseId),
+                userId: new Id(RequestAttributeExtractor::extractUser($request)->getId()),
+                purchaseBillingData: PurchaseTransformer::fromPayloadToPurchaseBillingData($content),
+                saveAsDefault: (bool) $content['save_as_default'] ?? false,
+            )
+        );
+
+        return new JsonResponse(
+            data: ApiResponseContent::createFromData(
+                []
+            )->toArray(),
+            status: Response::HTTP_OK,
+        );
+    }
+}
--- src/V2/Infrastructure/Listener/ExceptionListener.php
+++ src/V2/Infrastructure/Listener/ExceptionListener.php
@@ -16,8 +16,8 @@ use App\V2\Domain\Announcement\Exception\GetAnnouncementManagerException;
 use App\V2\Domain\Announcement\Exception\ManagerNotFoundException;
 use App\V2\Domain\Announcement\Exception\PostAnnouncementGroupUserException;
 use App\V2\Domain\Announcement\Exception\PutAnnouncementManagerException;
-use App\V2\Domain\Billing\Exception\BillingDataNotFoundException;
 use App\V2\Domain\Announcement\Exception\UserNotAuthorizedException;
+use App\V2\Domain\Billing\Exception\BillingDataNotFoundException;
 use App\V2\Domain\Course\Creator\CourseCreatorNotFoundException;
 use App\V2\Domain\Course\Creator\Exceptions\CreatorNotAuthorizedException;
 use App\V2\Domain\Course\Creator\Exceptions\CreatorNotFoundException;
@@ -34,6 +34,7 @@ use App\V2\Domain\LTI\Exceptions\PostLtiRegistrationException;
 use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
 use App\V2\Domain\Purchase\Exception\PostCreatePurchaseException;
 use App\V2\Domain\Purchase\Exception\PurchasableItemNotFoundException;
+use App\V2\Domain\Purchase\Exception\PurchaseNotAllowedException;
 use App\V2\Domain\Purchase\Exception\PurchaseNotFoundException;
 use App\V2\Domain\Shared\Criteria\CriteriaException;
 use App\V2\Domain\Shared\Criteria\InvalidSortException;
@@ -114,7 +115,8 @@ class ExceptionListener implements EventSubscriberInterface
             CreatorNotAuthorizedException::class,
             UserNotAuthorizedException::class,
             FirewallException::class,
-            UserForbiddenAction::class => Response::HTTP_FORBIDDEN,
+            UserForbiddenAction::class,
+            PurchaseNotAllowedException::class => Response::HTTP_FORBIDDEN,
             PostLtiRegistrationException::class,
             GetLtiRegistrationQueryException::class,
             GetCourseCreatorsException::class,
--- src/V2/Infrastructure/Persistence/Purchase/DBALPurchaseRepository.php
+++ src/V2/Infrastructure/Persistence/Purchase/DBALPurchaseRepository.php
@@ -8,6 +8,7 @@ use App\V2\Domain\Purchase\Exception\PurchaseItemNotFoundException;
 use App\V2\Domain\Purchase\Exception\PurchaseNotFoundException;
 use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
 use App\V2\Domain\Purchase\Purchase;
+use App\V2\Domain\Purchase\PurchaseBillingData;
 use App\V2\Domain\Purchase\PurchaseCollection;
 use App\V2\Domain\Purchase\PurchaseCriteria;
 use App\V2\Domain\Purchase\PurchaseItem;
@@ -251,6 +252,16 @@ readonly class DBALPurchaseRepository implements PurchaseRepository
             'currency_code' => CurrencyCodeTransformer::toString($purchase->getAmount()->currency()->code()),
             'tax_rate' => $purchase->getTaxRate()->value(),
             'tax_amount' => $purchase->getTaxAmount()->value(),
+            'billing_data' => $purchase->getBillingData() ? json_encode([
+                'tin' => $purchase->getBillingData()->getTin(),
+                'first_name' => $purchase->getBillingData()->getFirstName(),
+                'last_name' => $purchase->getBillingData()->getLastName(),
+                'address' => $purchase->getBillingData()->getAddress(),
+                'postal_code' => $purchase->getBillingData()->getPostalCode(),
+                'city' => $purchase->getBillingData()->getCity(),
+                'country' => $purchase->getBillingData()->getCountry(),
+                'metadata' => $purchase->getBillingData()->getMetadata(),
+            ]) : null,
             'created_at' => DBALDateTimeFormatter::format($purchase->getCreatedAt()),
             'updated_at' => DBALDateTimeFormatter::format($purchase->getUpdatedAt()),
             'deleted_at' => DBALDateTimeFormatter::format($purchase->getDeletedAt()),
@@ -263,6 +274,21 @@ readonly class DBALPurchaseRepository implements PurchaseRepository
      */
     private function fromArrayToPurchase(array $values): Purchase
     {
+        $billingData = null;
+        if (!empty($values['billing_data'])) {
+            $billingDataArray = json_decode($values['billing_data'], true) ?? [];
+            $billingData = new PurchaseBillingData(
+                tin: $billingDataArray['tin'] ?? '',
+                firstName: $billingDataArray['first_name'] ?? '',
+                lastName: $billingDataArray['last_name'] ?? '',
+                address: $billingDataArray['address'] ?? '',
+                postalCode: $billingDataArray['postal_code'] ?? '',
+                city: $billingDataArray['city'] ?? '',
+                country: $billingDataArray['country'] ?? '',
+                metadata: $billingDataArray['metadata'] ?? [],
+            );
+        }
+
         return new Purchase(
             id: new Uuid($values['id']),
             userId: new Id((int) $values['user_id']),
@@ -279,6 +305,7 @@ readonly class DBALPurchaseRepository implements PurchaseRepository
             createdAt: DBALDateTimeFormatter::parse($values['created_at']),
             updatedAt: DBALDateTimeFormatter::parse($values['updated_at']),
             deletedAt: DBALDateTimeFormatter::parse($values['deleted_at']),
+            billingData: $billingData,
         );
     }
 
--- src/V2/Infrastructure/Persistence/Purchase/InMemoryPurchaseRepository.php
+++ src/V2/Infrastructure/Persistence/Purchase/InMemoryPurchaseRepository.php
@@ -158,6 +158,7 @@ class InMemoryPurchaseRepository implements PurchaseRepository
                     createdAt: $purchase->getCreatedAt(),
                     updatedAt: $purchase->getUpdatedAt(),
                     deletedAt: $purchase->getDeletedAt(),
+                    billingData: $purchase->getBillingData(),
                 );
 
                 // Set purchase items if they exist
--- src/V2/Infrastructure/Purchase/PurchaseTransformer.php
+++ src/V2/Infrastructure/Purchase/PurchaseTransformer.php
@@ -6,6 +6,7 @@ namespace App\V2\Infrastructure\Purchase;
 
 use App\Entity\User;
 use App\V2\Domain\Purchase\Purchase;
+use App\V2\Domain\Purchase\PurchaseBillingData;
 use App\V2\Domain\Purchase\PurchaseCollection;
 use App\V2\Domain\Purchase\PurchaseItem;
 use App\V2\Domain\Purchase\PurchaseItemCollection;
@@ -78,4 +79,18 @@ class PurchaseTransformer
             'last_name' => $user->getLastName(),
         ];
     }
+
+    public static function fromPayloadToPurchaseBillingData(array $payload): PurchaseBillingData
+    {
+        return new PurchaseBillingData(
+            tin: $payload['tin'],
+            firstName: $payload['first_name'],
+            lastName: $payload['last_name'],
+            address: $payload['address'],
+            postalCode: $payload['postal_code'],
+            city: $payload['city'],
+            country: $payload['country'],
+            metadata: $payload['metadata'] ?? [],
+        );
+    }
 }
--- src/V2/Infrastructure/Validator/Campus/PurchaseBillingDataValidator.php
+++ src/V2/Infrastructure/Validator/Campus/PurchaseBillingDataValidator.php
@@ -0,0 +1,71 @@
+<?php
+
+declare(strict_types=1);
+
+namespace App\V2\Infrastructure\Validator\Campus;
+
+use App\V2\Infrastructure\Validator\CommonValidator;
+use App\V2\Infrastructure\Validator\ValidatorException;
+use Symfony\Component\Validator\Constraints;
+
+class PurchaseBillingDataValidator extends CommonValidator
+{
+    /**
+     * @throws ValidatorException
+     */
+    public static function validatePurchaseBillingData(array $data): void
+    {
+        $constraints = [
+            new Constraints\NotBlank(message: 'Body cannot be empty'),
+            new Constraints\Collection([
+                'tin' => [
+                    new Constraints\NotBlank(),
+                    new Constraints\Type('string'),
+                    new Constraints\Length(max: 50),
+                ],
+                'first_name' => [
+                    new Constraints\NotBlank(),
+                    new Constraints\Type('string'),
+                    new Constraints\Length(max: 100),
+                ],
+                'last_name' => [
+                    new Constraints\NotBlank(),
+                    new Constraints\Type('string'),
+                    new Constraints\Length(max: 100),
+                ],
+                'address' => [
+                    new Constraints\NotBlank(),
+                    new Constraints\Type('string'),
+                    new Constraints\Length(max: 255),
+                ],
+                'postal_code' => [
+                    new Constraints\NotBlank(),
+                    new Constraints\Type('string'),
+                    new Constraints\Length(max: 20),
+                ],
+                'city' => [
+                    new Constraints\NotBlank(),
+                    new Constraints\Type('string'),
+                    new Constraints\Length(max: 100),
+                ],
+                'country' => [
+                    new Constraints\NotBlank(),
+                    new Constraints\Type('string'),
+                    new Constraints\Length(min: 2, max: 3),
+                    new Constraints\Regex(
+                        pattern: '/^[A-Z]{2,3}$/',
+                        message: 'Country must be a valid ISO country code (2-3 uppercase letters)'
+                    ),
+                ],
+                'metadata' => [
+                    new Constraints\Type('array'),
+                ],
+                'save_as_default' => new Constraints\Optional([
+                    new Constraints\Type('bool'),
+                ]),
+            ]),
+        ];
+
+        parent::validate($data, $constraints);
+    }
+}
--- src/V2/Infrastructure/config/routes/campus.yaml
+++ src/V2/Infrastructure/config/routes/campus.yaml
@@ -23,6 +23,11 @@ post_create_purchase:
   methods: POST
   controller: App\V2\Infrastructure\Controller\Campus\PostCreatePurchaseController
 
+patch_purchase_billing_data:
+  path: /purchases/{purchaseId}/billing-data
+  methods: PATCH
+  controller: App\V2\Infrastructure\Controller\Campus\PatchPurchaseBillingDataController
+
 get_campus_purchase:
   path: /purchases/{purchaseId}
   methods: GET
--- tests/Functional/HelperTrait/Endpoints/Campus/CampusPurchaseEndpoints.php
+++ tests/Functional/HelperTrait/Endpoints/Campus/CampusPurchaseEndpoints.php
@@ -15,4 +15,9 @@ class CampusPurchaseEndpoints
     {
         return self::purchasesEndpoint() . '/' . $purchaseId;
     }
+
+    public static function patchPurchaseBillingDataEndpoint(string $purchaseId): string
+    {
+        return self::getPurchaseEndpoint($purchaseId) . '/billing-data';
+    }
 }
--- tests/Functional/V2/Campus/Purchase/PatchPurchaseBillingDataControllerFunctionalTest.php
+++ tests/Functional/V2/Campus/Purchase/PatchPurchaseBillingDataControllerFunctionalTest.php
@@ -0,0 +1,532 @@
+<?php
+
+declare(strict_types=1);
+
+namespace App\Tests\Functional\V2\Campus\Purchase;
+
+use App\Entity\User;
+use App\Tests\Functional\FunctionalTestCase;
+use App\Tests\Functional\HelperTrait\Endpoints\Campus\CampusPurchaseEndpoints;
+use App\Tests\Functional\HelperTrait\PurchasableItemHelperTrait;
+use App\Tests\V2\Mother\Billing\BillingDataMother;
+use App\Tests\V2\Mother\Purchase\PurchaseMother;
+use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
+use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
+use App\V2\Domain\Billing\BillingDataRepository;
+use App\V2\Domain\Billing\Exception\BillingDataRepositoryException;
+use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
+use App\V2\Domain\Purchase\PurchaseRepository;
+use App\V2\Domain\Purchase\PurchaseStatus;
+use App\V2\Domain\Shared\Financial\Currency;
+use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
+use App\V2\Domain\Shared\Id\Id;
+use App\V2\Domain\Shared\Uuid\InvalidUuidException;
+use Doctrine\DBAL\Exception;
+use Doctrine\ORM\Exception\ORMException;
+use Doctrine\ORM\OptimisticLockException;
+use Doctrine\Persistence\Mapping\MappingException;
+use PHPUnit\Framework\Attributes\DataProvider;
+use Symfony\Component\HttpFoundation\Response;
+
+class PatchPurchaseBillingDataControllerFunctionalTest extends FunctionalTestCase
+{
+    use PurchasableItemHelperTrait;
+
+    private const string VALID_UUID = '550e8400-e29b-41d4-a716-************';
+    private const string INVALID_UUID = 'invalid-uuid';
+
+    private PurchaseRepository $purchaseRepository;
+    private BillingDataRepository $billingDataRepository;
+    private User $testUser;
+
+    /**
+     * @throws OptimisticLockException
+     * @throws ORMException
+     */
+    protected function setUp(): void
+    {
+        parent::setUp();
+
+        /** @var PurchaseRepository $purchaseRepository */
+        $purchaseRepository = $this->getService(PurchaseRepository::class);
+        $this->purchaseRepository = $purchaseRepository;
+
+        /** @var BillingDataRepository $billingDataRepository */
+        $billingDataRepository = $this->getService(BillingDataRepository::class);
+        $this->billingDataRepository = $billingDataRepository;
+
+        $this->testUser = $this->createAndGetUser(
+            roles: [User::ROLE_USER],
+            email: 'test.patch.billing.' . uniqid() . '@example.com',
+        );
+    }
+
+    /**
+     * @throws MappingException
+     * @throws Exception
+     */
+    protected function tearDown(): void
+    {
+        if (!empty($this->testUser)) {
+            $this->hardDeleteUsersByIds([$this->testUser->getId()]);
+        }
+
+        parent::tearDown();
+    }
+
+    /**
+     * @throws InvalidUuidException
+     * @throws PurchaseRepositoryException
+     * @throws InvalidCurrencyCodeException
+     */
+    public function testSuccessfulPatchBillingData(): void
+    {
+        $userToken = $this->loginAndGetTokenForUser($this->testUser);
+
+        // Create a purchase in the database first
+        $purchase = PurchaseMother::create(
+            userId: new Id($this->testUser->getId()),
+            status: PurchaseStatus::Pending,
+            amount: MoneyMother::create(amount: 5000, currency: Currency::EUR())
+        );
+        $this->purchaseRepository->put($purchase);
+
+        $purchaseId = $purchase->getId()->value();
+
+        $requestBody = [
+            'tin' => '12345678A',
+            'first_name' => 'John',
+            'last_name' => 'Doe',
+            'address' => '123 Main Street',
+            'postal_code' => '12345',
+            'city' => 'Madrid',
+            'country' => 'ES',
+            'metadata' => ['key' => 'value'],
+            'save_as_default' => false,
+        ];
+
+        $response = $this->makeRequest(
+            method: 'PATCH',
+            uri: CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint($purchaseId),
+            body: $requestBody,
+            bearerToken: $userToken
+        );
+
+        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
+        $this->assertJson($response->getContent());
+
+        $responseData = json_decode($response->getContent(), true);
+        $this->assertArrayHasKey('data', $responseData);
+        $this->assertEquals([], $responseData['data']);
+    }
+
+    /**
+     * @throws InvalidUuidException
+     * @throws PurchaseRepositoryException
+     * @throws InvalidCurrencyCodeException
+     * @throws BillingDataRepositoryException
+     */
+    public function testSuccessfulPatchBillingDataWithSaveAsDefault(): void
+    {
+        $userToken = $this->loginAndGetTokenForUser($this->testUser);
+
+        // Create a purchase in the database first
+        $purchase = PurchaseMother::create(
+            userId: new Id($this->testUser->getId()),
+            status: PurchaseStatus::Pending,
+            amount: MoneyMother::create(amount: 3000, currency: Currency::EUR())
+        );
+        $this->purchaseRepository->put($purchase);
+
+        // Create a billing data record for the user (required for save_as_default functionality)
+        $existingBillingData = BillingDataMother::create(
+            userId: new Id($this->testUser->getId())
+        );
+        $this->billingDataRepository->put($existingBillingData);
+
+        $purchaseId = $purchase->getId()->value();
+
+        $requestBody = [
+            'tin' => '87654321B',
+            'first_name' => 'Jane',
+            'last_name' => 'Smith',
+            'address' => '456 Oak Avenue',
+            'postal_code' => '54321',
+            'city' => 'Barcelona',
+            'country' => 'ES',
+            'metadata' => ['company' => 'Test Corp'],
+            'save_as_default' => true,
+        ];
+
+        $response = $this->makeRequest(
+            method: 'PATCH',
+            uri: CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint($purchaseId),
+            body: $requestBody,
+            bearerToken: $userToken
+        );
+
+        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
+        $this->assertJson($response->getContent());
+
+        $responseData = json_decode($response->getContent(), true);
+        $this->assertArrayHasKey('data', $responseData);
+        $this->assertEquals([], $responseData['data']);
+    }
+
+    /**
+     * @throws InvalidUuidException
+     * @throws PurchaseRepositoryException
+     * @throws InvalidCurrencyCodeException
+     */
+    #[DataProvider('invalidDataProvider')]
+    public function testValidationErrors(
+        array $requestBody,
+        int $expectedStatusCode,
+    ): void {
+        $userToken = $this->loginAndGetTokenForUser($this->testUser);
+
+        // Create a purchase in the database first
+        $purchase = PurchaseMother::create(
+            userId: new Id($this->testUser->getId()),
+            status: PurchaseStatus::Pending,
+            amount: MoneyMother::create(amount: 2000, currency: Currency::EUR())
+        );
+        $this->purchaseRepository->put($purchase);
+
+        $purchaseId = $purchase->getId()->value();
+
+        $response = $this->makeRequest(
+            method: 'PATCH',
+            uri: CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint($purchaseId),
+            body: $requestBody,
+            bearerToken: $userToken
+        );
+
+        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
+        $this->assertJson($response->getContent());
+
+        $responseData = json_decode($response->getContent(), true);
+        $this->assertArrayHasKey('error', $responseData);
+        $this->assertEquals(1, $responseData['error']);
+        $this->assertArrayHasKey('message', $responseData);
+        $this->assertEquals('Validation failed', $responseData['message']);
+    }
+
+    public function testInvalidUuidFormat(): void
+    {
+        $userToken = $this->loginAndGetTokenForUser($this->testUser);
+
+        $requestBody = [
+            'tin' => '12345678A',
+            'first_name' => 'John',
+            'last_name' => 'Doe',
+            'address' => '123 Main Street',
+            'postal_code' => '12345',
+            'city' => 'Madrid',
+            'country' => 'ES',
+            'metadata' => [],
+        ];
+
+        $response = $this->makeRequest(
+            method: 'PATCH',
+            uri: CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint(self::INVALID_UUID),
+            body: $requestBody,
+            bearerToken: $userToken
+        );
+
+        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
+        $this->assertJson($response->getContent());
+
+        $responseData = json_decode($response->getContent(), true);
+        $this->assertArrayHasKey('error', $responseData);
+        $this->assertEquals(1, $responseData['error']);
+    }
+
+    public function testUnauthenticatedAccess(): void
+    {
+        $purchaseId = UuidMother::create()->value();
+
+        $requestBody = [
+            'tin' => '12345678A',
+            'first_name' => 'John',
+            'last_name' => 'Doe',
+            'address' => '123 Main Street',
+            'postal_code' => '12345',
+            'city' => 'Madrid',
+            'country' => 'ES',
+            'metadata' => [],
+        ];
+
+        $response = $this->makeRequest(
+            method: 'PATCH',
+            uri: "/api/v2/purchases/{$purchaseId}/billing-data",
+            body: $requestBody
+        );
+
+        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
+    }
+
+    /**
+     * @throws InvalidUuidException
+     * @throws PurchaseRepositoryException
+     * @throws InvalidCurrencyCodeException
+     */
+    public function testEmptyRequestBody(): void
+    {
+        $userToken = $this->loginAndGetTokenForUser($this->testUser);
+
+        // Create a purchase in the database first
+        $purchase = PurchaseMother::create(
+            userId: new Id($this->testUser->getId()),
+            status: PurchaseStatus::Pending,
+            amount: MoneyMother::create(amount: 1500, currency: Currency::EUR())
+        );
+        $this->purchaseRepository->put($purchase);
+
+        $purchaseId = $purchase->getId()->value();
+
+        $response = $this->makeRequest(
+            method: 'PATCH',
+            uri: CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint($purchaseId),
+            body: [],
+            bearerToken: $userToken
+        );
+
+        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
+        $this->assertJson($response->getContent());
+
+        $responseData = json_decode($response->getContent(), true);
+        $this->assertArrayHasKey('error', $responseData);
+        $this->assertEquals(1, $responseData['error']);
+        $this->assertArrayHasKey('message', $responseData);
+        $this->assertEquals('Validation failed', $responseData['message']);
+    }
+
+    /**
+     * @throws InvalidUuidException
+     * @throws PurchaseRepositoryException
+     * @throws InvalidCurrencyCodeException
+     */
+    public function testMalformedJsonRequest(): void
+    {
+        $userToken = $this->loginAndGetTokenForUser($this->testUser);
+
+        // Create a purchase in the database first
+        $purchase = PurchaseMother::create(
+            userId: new Id($this->testUser->getId()),
+            status: PurchaseStatus::Pending,
+            amount: MoneyMother::create(amount: 1000, currency: Currency::EUR())
+        );
+        $this->purchaseRepository->put($purchase);
+
+        $purchaseId = $purchase->getId()->value();
+
+        $this->client->request(
+            'PATCH',
+            'http://localhost' . CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint($purchaseId),
+            [],
+            [],
+            ['HTTP_Authorization' => 'Bearer ' . $userToken],
+            'invalid-json'
+        );
+
+        $response = $this->client->getResponse();
+        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
+    }
+
+    /**
+     * @throws InvalidUuidException
+     * @throws PurchaseRepositoryException
+     * @throws InvalidCurrencyCodeException
+     */
+    public function testSuccessfulPatchBillingDataWithSaveAsDefaultWhenNoBillingDataExists(): void
+    {
+        $userToken = $this->loginAndGetTokenForUser($this->testUser);
+
+        // Create a purchase in the database first
+        $purchase = PurchaseMother::create(
+            userId: new Id($this->testUser->getId()),
+            status: PurchaseStatus::Pending,
+            amount: MoneyMother::create(amount: 2500, currency: Currency::EUR())
+        );
+        $this->purchaseRepository->put($purchase);
+
+        // NOTE: We don't create a BillingData record for the user to test the case where it doesn't exist
+
+        $purchaseId = $purchase->getId()->value();
+
+        $requestBody = [
+            'tin' => '11111111C',
+            'first_name' => 'Carlos',
+            'last_name' => 'Rodriguez',
+            'address' => '321 New Street',
+            'postal_code' => '98765',
+            'city' => 'Sevilla',
+            'country' => 'ES',
+            'metadata' => ['new_user' => true],
+            'save_as_default' => true,
+        ];
+
+        $response = $this->makeRequest(
+            method: 'PATCH',
+            uri: CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint($purchaseId),
+            body: $requestBody,
+            bearerToken: $userToken
+        );
+
+        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
+        $this->assertJson($response->getContent());
+
+        $responseData = json_decode($response->getContent(), true);
+        $this->assertArrayHasKey('data', $responseData);
+        $this->assertEquals([], $responseData['data']);
+    }
+
+    /**
+     * @throws OptimisticLockException
+     * @throws ORMException
+     * @throws Exception
+     * @throws InvalidUuidException
+     * @throws PurchaseRepositoryException
+     * @throws InvalidCurrencyCodeException
+     */
+    public function testAdminRoleAccess(): void
+    {
+        // Create admin user
+        $adminUser = $this->createAndGetUser(
+            roles: [User::ROLE_ADMIN],
+            email: 'admin.patch.billing.' . uniqid() . '@example.com',
+        );
+
+        $userToken = $this->loginAndGetTokenForUser($adminUser);
+
+        // Create a purchase in the database first
+        $purchase = PurchaseMother::create(
+            userId: new Id($adminUser->getId()),
+            status: PurchaseStatus::Pending,
+            amount: MoneyMother::create(amount: 4000, currency: Currency::EUR())
+        );
+        $this->purchaseRepository->put($purchase);
+
+        $purchaseId = $purchase->getId()->value();
+
+        $requestBody = [
+            'tin' => '12345678A',
+            'first_name' => 'Admin',
+            'last_name' => 'User',
+            'address' => '789 Admin Street',
+            'postal_code' => '67890',
+            'city' => 'Valencia',
+            'country' => 'ES',
+            'metadata' => [],
+            'save_as_default' => false,
+        ];
+
+        $response = $this->makeRequest(
+            method: 'PATCH',
+            uri: CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint($purchaseId),
+            body: $requestBody,
+            bearerToken: $userToken
+        );
+
+        // Should have access with ROLE_ADMIN
+        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
+
+        // Clean up admin user
+        $this->hardDeleteUsersByIds([$adminUser->getId()]);
+    }
+
+    public static function invalidDataProvider(): \Generator
+    {
+        yield 'missing tin field' => [
+            'requestBody' => [
+                'first_name' => 'John',
+                'last_name' => 'Doe',
+                'address' => '123 Main Street',
+                'postal_code' => '12345',
+                'city' => 'Madrid',
+                'country' => 'ES',
+                'metadata' => [],
+            ],
+            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
+        ];
+
+        yield 'missing first_name field' => [
+            'requestBody' => [
+                'tin' => '12345678A',
+                'last_name' => 'Doe',
+                'address' => '123 Main Street',
+                'postal_code' => '12345',
+                'city' => 'Madrid',
+                'country' => 'ES',
+                'metadata' => [],
+            ],
+            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
+        ];
+
+        yield 'missing last_name field' => [
+            'requestBody' => [
+                'tin' => '12345678A',
+                'first_name' => 'John',
+                'address' => '123 Main Street',
+                'postal_code' => '12345',
+                'city' => 'Madrid',
+                'country' => 'ES',
+                'metadata' => [],
+            ],
+            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
+        ];
+
+        yield 'missing address field' => [
+            'requestBody' => [
+                'tin' => '12345678A',
+                'first_name' => 'John',
+                'last_name' => 'Doe',
+                'postal_code' => '12345',
+                'city' => 'Madrid',
+                'country' => 'ES',
+                'metadata' => [],
+            ],
+            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
+        ];
+
+        yield 'missing postal_code field' => [
+            'requestBody' => [
+                'tin' => '12345678A',
+                'first_name' => 'John',
+                'last_name' => 'Doe',
+                'address' => '123 Main Street',
+                'city' => 'Madrid',
+                'country' => 'ES',
+                'metadata' => [],
+            ],
+            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
+        ];
+
+        yield 'missing city field' => [
+            'requestBody' => [
+                'tin' => '12345678A',
+                'first_name' => 'John',
+                'last_name' => 'Doe',
+                'address' => '123 Main Street',
+                'postal_code' => '12345',
+                'country' => 'ES',
+                'metadata' => [],
+            ],
+            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
+        ];
+
+        yield 'missing country field' => [
+            'requestBody' => [
+                'tin' => '12345678A',
+                'first_name' => 'John',
+                'last_name' => 'Doe',
+                'address' => '123 Main Street',
+                'postal_code' => '12345',
+                'city' => 'Madrid',
+                'metadata' => [],
+            ],
+            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
+        ];
+    }
+}
--- tests/V2/Infrastructure/Persistence/Purchase/DBALPurchaseRepositoryTest.php
+++ tests/V2/Infrastructure/Persistence/Purchase/DBALPurchaseRepositoryTest.php
@@ -52,6 +52,7 @@ class DBALPurchaseRepositoryTest extends PurchaseRepositoryTestCase
         $table->addColumn('currency_code', 'string', ['length' => 3]);
         $table->addColumn('tax_rate', 'float');
         $table->addColumn('tax_amount', 'integer');
+        $table->addColumn('billing_data', 'text', ['notnull' => false]);
         $table->addColumn('created_at', 'datetime');
         $table->addColumn('updated_at', 'datetime', ['notnull' => false]);
         $table->addColumn('deleted_at', 'datetime', ['notnull' => false]);
--- tests/V2/Infrastructure/Validator/Campus/PurchaseBillingDataValidatorTest.php
+++ tests/V2/Infrastructure/Validator/Campus/PurchaseBillingDataValidatorTest.php
@@ -0,0 +1,260 @@
+<?php
+
+declare(strict_types=1);
+
+namespace App\Tests\V2\Infrastructure\Validator\Campus;
+
+use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
+use App\V2\Infrastructure\Validator\Campus\PurchaseBillingDataValidator;
+use App\V2\Infrastructure\Validator\ValidatorException;
+use PHPUnit\Framework\Attributes\DataProvider;
+
+class PurchaseBillingDataValidatorTest extends ValidatorTestCase
+{
+    /**
+     * Test basic functionality - validator correctly validates purchase billing data.
+     *
+     * @throws ValidatorException
+     */
+    #[DataProvider('validateSuccessProvider')]
+    public function testValidateSuccess(array $payload): void
+    {
+        $this->expectNotToPerformAssertions();
+
+        PurchaseBillingDataValidator::validatePurchaseBillingData($payload);
+    }
+
+    public static function validateSuccessProvider(): \Generator
+    {
+        yield 'Valid complete billing data' => [
+            [
+                'tin' => '12345678A',
+                'first_name' => 'John',
+                'last_name' => 'Doe',
+                'address' => '123 Main Street',
+                'postal_code' => '12345',
+                'city' => 'Madrid',
+                'country' => 'ES',
+                'metadata' => [],
+                'save_as_default' => true,
+            ],
+        ];
+
+        yield 'Valid billing data with 3-letter country code' => [
+            [
+                'tin' => 'B12345678',
+                'first_name' => 'Jane',
+                'last_name' => 'Smith',
+                'address' => '456 Oak Avenue',
+                'postal_code' => '67890',
+                'city' => 'Barcelona',
+                'country' => 'ESP',
+                'metadata' => ['company' => 'Test Corp'],
+                'save_as_default' => false,
+            ],
+        ];
+
+        yield 'Valid billing data without optional save_as_default' => [
+            [
+                'tin' => 'X1234567L',
+                'first_name' => 'Carlos',
+                'last_name' => 'García',
+                'address' => '789 Pine Street',
+                'postal_code' => '54321',
+                'city' => 'Valencia',
+                'country' => 'ES',
+                'metadata' => [],
+            ],
+        ];
+
+        yield 'Valid billing data with maximum length fields' => [
+            [
+                'tin' => str_repeat('A', 50),
+                'first_name' => str_repeat('B', 100),
+                'last_name' => str_repeat('C', 100),
+                'address' => str_repeat('D', 255),
+                'postal_code' => str_repeat('E', 20),
+                'city' => str_repeat('F', 100),
+                'country' => 'US',
+                'metadata' => ['key1' => 'value1', 'key2' => 'value2'],
+                'save_as_default' => true,
+            ],
+        ];
+
+        yield 'Valid billing data with complex metadata' => [
+            [
+                'tin' => '98765432B',
+                'first_name' => 'María',
+                'last_name' => 'López',
+                'address' => 'Calle Mayor 123',
+                'postal_code' => '28001',
+                'city' => 'Madrid',
+                'country' => 'ES',
+                'metadata' => [
+                    'company' => 'Example S.L.',
+                    'department' => 'IT',
+                    'notes' => 'Special billing requirements',
+                ],
+                'save_as_default' => false,
+            ],
+        ];
+
+        yield 'Valid billing data with save_as_default true' => [
+            [
+                'tin' => '87654321C',
+                'first_name' => 'Ana',
+                'last_name' => 'Martín',
+                'address' => 'Avenida Principal 456',
+                'postal_code' => '08001',
+                'city' => 'Barcelona',
+                'country' => 'ES',
+                'metadata' => [],
+                'save_as_default' => true,
+            ],
+        ];
+    }
+
+    /**
+     * Test comprehensive validation coverage and error handling.
+     */
+    #[DataProvider('validateFailProvider')]
+    public function testValidateFail(array $payload, array $violations): void
+    {
+        try {
+            PurchaseBillingDataValidator::validatePurchaseBillingData($payload);
+            $this->fail('Expected ValidatorException was not thrown');
+        } catch (ValidatorException $e) {
+            $this->assertViolations($violations, $e->getViolations());
+        }
+    }
+
+    public static function validateFailProvider(): \Generator
+    {
+        yield 'Empty body' => [
+            [],
+            [
+                '' => 'Body cannot be empty',
+                '[tin]' => 'This field is missing.',
+                '[first_name]' => 'This field is missing.',
+                '[last_name]' => 'This field is missing.',
+                '[address]' => 'This field is missing.',
+                '[postal_code]' => 'This field is missing.',
+                '[city]' => 'This field is missing.',
+                '[country]' => 'This field is missing.',
+                '[metadata]' => 'This field is missing.',
+            ],
+        ];
+
+        yield 'Missing required tin field' => [
+            [
+                'first_name' => 'John',
+                'last_name' => 'Doe',
+                'address' => '123 Main Street',
+                'postal_code' => '12345',
+                'city' => 'Madrid',
+                'country' => 'ES',
+                'metadata' => [],
+            ],
+            [
+                '[tin]' => 'This field is missing.',
+            ],
+        ];
+
+        yield 'Empty tin field' => [
+            [
+                'tin' => '',
+                'first_name' => 'John',
+                'last_name' => 'Doe',
+                'address' => '123 Main Street',
+                'postal_code' => '12345',
+                'city' => 'Madrid',
+                'country' => 'ES',
+                'metadata' => [],
+            ],
+            [
+                '[tin]' => 'This value should not be blank.',
+            ],
+        ];
+
+        yield 'Invalid tin type' => [
+            [
+                'tin' => 123,
+                'first_name' => 'John',
+                'last_name' => 'Doe',
+                'address' => '123 Main Street',
+                'postal_code' => '12345',
+                'city' => 'Madrid',
+                'country' => 'ES',
+                'metadata' => [],
+            ],
+            [
+                '[tin]' => 'This value should be of type string.',
+            ],
+        ];
+
+        yield 'Tin too long' => [
+            [
+                'tin' => str_repeat('A', 51),
+                'first_name' => 'John',
+                'last_name' => 'Doe',
+                'address' => '123 Main Street',
+                'postal_code' => '12345',
+                'city' => 'Madrid',
+                'country' => 'ES',
+                'metadata' => [],
+            ],
+            [
+                '[tin]' => 'This value is too long. It should have 50 characters or less.',
+            ],
+        ];
+
+        yield 'Invalid country format - lowercase' => [
+            [
+                'tin' => '12345678A',
+                'first_name' => 'John',
+                'last_name' => 'Doe',
+                'address' => '123 Main Street',
+                'postal_code' => '12345',
+                'city' => 'Madrid',
+                'country' => 'es',
+                'metadata' => [],
+            ],
+            [
+                '[country]' => 'Country must be a valid ISO country code (2-3 uppercase letters)',
+            ],
+        ];
+
+        yield 'Invalid metadata type' => [
+            [
+                'tin' => '12345678A',
+                'first_name' => 'John',
+                'last_name' => 'Doe',
+                'address' => '123 Main Street',
+                'postal_code' => '12345',
+                'city' => 'Madrid',
+                'country' => 'ES',
+                'metadata' => 'invalid',
+            ],
+            [
+                '[metadata]' => 'This value should be of type array.',
+            ],
+        ];
+
+        yield 'Invalid save_as_default type' => [
+            [
+                'tin' => '12345678A',
+                'first_name' => 'John',
+                'last_name' => 'Doe',
+                'address' => '123 Main Street',
+                'postal_code' => '12345',
+                'city' => 'Madrid',
+                'country' => 'ES',
+                'metadata' => [],
+                'save_as_default' => 'true',
+            ],
+            [
+                '[save_as_default]' => 'This value should be of type bool.',
+            ],
+        ];
+    }
+}
--- tests/V2/Mother/Purchase/PurchaseBillingDataMother.php
+++ tests/V2/Mother/Purchase/PurchaseBillingDataMother.php
@@ -0,0 +1,32 @@
+<?php
+
+declare(strict_types=1);
+
+namespace App\Tests\V2\Mother\Purchase;
+
+use App\V2\Domain\Purchase\PurchaseBillingData;
+
+class PurchaseBillingDataMother
+{
+    public static function create(
+        ?string $tin = null,
+        ?string $firstName = null,
+        ?string $lastName = null,
+        ?string $address = null,
+        ?string $postalCode = null,
+        ?string $city = null,
+        ?string $country = null,
+        ?array $metadata = null,
+    ): PurchaseBillingData {
+        return new PurchaseBillingData(
+            tin: $tin ?? '12345678A',
+            firstName: $firstName ?? 'John',
+            lastName: $lastName ?? 'Doe',
+            address: $address ?? '123 Main Street',
+            postalCode: $postalCode ?? '12345',
+            city: $city ?? 'Madrid',
+            country: $country ?? 'Spain',
+            metadata: $metadata ?? [],
+        );
+    }
+}
--- tests/V2/Mother/Purchase/PurchaseMother.php
+++ tests/V2/Mother/Purchase/PurchaseMother.php
@@ -9,6 +9,7 @@ use App\Tests\V2\Mother\Shared\Financial\TaxRateMother;
 use App\Tests\V2\Mother\Shared\Id\IdMother;
 use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
 use App\V2\Domain\Purchase\Purchase;
+use App\V2\Domain\Purchase\PurchaseBillingData;
 use App\V2\Domain\Purchase\PurchaseStatus;
 use App\V2\Domain\Shared\Financial\Money;
 use App\V2\Domain\Shared\Financial\TaxRate;
@@ -29,6 +30,7 @@ class PurchaseMother
         ?Money $amount = null,
         ?TaxRate $taxRate = null,
         ?Money $taxAmount = null,
+        ?PurchaseBillingData $billingData = null,
         ?\DateTimeImmutable $createdAt = null,
         ?\DateTimeImmutable $updatedAt = null,
         ?\DateTimeImmutable $deletedAt = null,
@@ -43,6 +45,7 @@ class PurchaseMother
             createdAt: $createdAt ?? new \DateTimeImmutable(),
             updatedAt: $updatedAt,
             deletedAt: $deletedAt,
+            billingData: $billingData,
         );
     }
 }
