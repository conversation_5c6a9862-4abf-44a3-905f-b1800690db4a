[
  {
    "id": 2721895557,
    "type": null,
    "body": "left review comments",
    "author": {
      "id": 22588622,
      "username": "jonandoni.ezquerra",
      "public_email": "",
      "name": "<PERSON>",
      "state": "active",
      "locked": false,
      "avatar_url": "https://gitlab.com/uploads/-/system/user/avatar/22588622/avatar.png",
      "web_url": "https://gitlab.com/jonandoni.ezquerra"
    },
    "created_at": "2025-09-01T11:53:11.065Z",
    "updated_at": "2025-09-01T11:53:11.069Z",
    "system": true,
    "noteable_id": 411139248,
    "noteable_type": "MergeRequest",
    "project_id": 60982345,
    "resolvable": false,
    "confidential": false,
    "internal": false,
    "imported": false,
    "imported_from": "none",
    "noteable_iid": 1203,
    "commands_changes": {}
  },
  {
    "id": **********,
    "type": "DiffNote",
    "body": "Este test `testFromCollectionToArrayWithM
ultipleCourses` y este `testFromCollectionToArrayWithSingleCourse` podemos unificarlos con un provider",
    "author": {
      "id": 22588622,
      "username": "jonandoni.ezquerra",
      "public_email": "",
      "name": "Jon Andoni Ezquerra",
      "state": "active",
      "locked": false,
      "avatar_url": "https://gitlab.com/uploads/-/system/user/avatar/22588622/avatar.png",
      "web_url": "https://gitlab.com/jonandoni.ezquerra"
    },
    "created_at": "2025-09-01T11:53:10.719Z",
    "updated_at": "2025-09-01T11:53:10.719Z",
    "system": false,
    "noteable_id": 411139248,
    "noteable_type": "MergeRequest",
    "project_id": 60982345,
    "commit_id": null,
    "position": {
      "base_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "start_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "head_sha": "8a6e3cec5cfa29f7051a2ff2251a54c340c218af",
      "old_path": "tests/V2/Infrastructure/Course/Manager/CourseManagerTransformerTest.php",
      "new_path": "tests/V2/Infrastructure/Course/Manager/CourseManagerTransformerTest.php",
      "position_type": "file"
    },
    "resolvable": true,
    "resolved": false,
    "resolved_by": null,
    "resolved_at": null,
    "suggestions": [],
    "confidential": false,
    "internal": false,
    "imported": false,
    "imported_from": "none",
    "noteable_iid": 1203,
    "commands_changes": {}
  },
  {
    "id": 2721895500,
    "type": "DiffNote",
    "body": "Este test es redundante con los anteriores.",
    "author": {
      "id": 22588622,
      "username": "jonandoni.ezquerra",
      "public_email": "",
      "name": "Jon Andoni Ezquerra",
      "state": "active",
      "locked": false,
      "avatar_url": "https://gitlab.com/uploads/-/system/user/avatar/22588622/avatar.png",
      "web_url": "https://gitlab.com/jonandoni.ezquerra"
    },
    "created_at": "2025-09-01T11:53:10.593Z",
    "updated_at": "2025-09-01T11:53:10.593Z",
    "system": false,
    "noteable_id": 411139248,
    "noteable_type": "MergeRequest",
    "project_id": 60982345,
    "commit_id": null,
    "position": {
      "base_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "start_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "head_sha": "8a6e3cec5cfa29f7051a2ff2251a54c340c218af",
      "old_path": "tests/V2/Infrastructure/Course/Manager/CourseManagerTransformerTest.php",
      "new_path": "tests/V2/Infrastructure/Course/Manager/CourseManagerTransformerTest.php",
      "position_type": "text",
      "old_line": null,
      "new_line": 102,
      "line_range": {
        "start": {
          "line_code": "c16cd5946157701bd96c83cc4d77ec207cba2bce_0_83",
          "type": "new",
          "old_line": null,
          "new_line": 83
        },
        "end": {
          "line_code": "c16cd5946157701bd96c83cc4d77ec207cba2bce_0_102",
          "type": "new",
          "old_line": null,
          "new_line": 102
        }
      }
    },
    "resolvable": true,
    "resolved": false,
    "resolved_by": null,
    "resolved_at": null,
    "suggestions": [],
    "confidential": false,
    "internal": false,
    "imported": false,
    "imported_from": "none",
    "noteable_iid": 1203,
    "commands_changes": {}
  },
  {
    "id": 2721895489,
    "type": "DiffNote",
    "body": "No creemos el mock de una clase que no es
 un servicio que se inyecta, tenemos el UserMother para poder crear un objeto te tipo usuario real.",
    "author": {
      "id": 22588622,
      "username": "jonandoni.ezquerra",
      "public_email": "",
      "name": "Jon Andoni Ezquerra",
      "state": "active",
      "locked": false,
      "avatar_url": "https://gitlab.com/uploads/-/system/user/avatar/22588622/avatar.png",
      "web_url": "https://gitlab.com/jonandoni.ezquerra"
    },
    "created_at": "2025-09-01T11:53:10.390Z",
    "updated_at": "2025-09-01T11:53:10.390Z",
    "system": false,
    "noteable_id": 411139248,
    "noteable_type": "MergeRequest",
    "project_id": 60982345,
    "commit_id": null,
    "position": {
      "base_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "start_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "head_sha": "8a6e3cec5cfa29f7051a2ff2251a54c340c218af",
      "old_path": "tests/V2/Application/QueryHandler/Admin/GetManagerCoursesHandlerTest.php",
      "new_path": "tests/V2/Application/QueryHandler/Admin/GetManagerCoursesHandlerTest.php",
      "position_type": "text",
      "old_line": null,
      "new_line": 131,
      "line_range": {
        "start": {
          "line_code": "94903149c23696e529ddb410158edd739cf5b250_0_130",
          "type": "new",
          "old_line": null,
          "new_line": 130
        },
        "end": {
          "line_code": "94903149c23696e529ddb410158edd739cf5b250_0_131",
          "type": "new",
          "old_line": null,
          "new_line": 131
        }
      }
    },
    "resolvable": true,
    "resolved": false,
    "resolved_by": null,
    "resolved_at": null,
    "suggestions": [],
    "confidential": false,
    "internal": false,
    "imported": false,
    "imported_from": "none",
    "noteable_iid": 1203,
    "commands_changes": {}
  },
  {
    "id": 2721895481,
    "type": "DiffNote",
    "body": "No creemos el mock de una clase que no es
 un servicio que se inyecta, tenemos el UserMother para poder crear un objeto te tipo usuario real.",
    "author": {
      "id": 22588622,
      "username": "jonandoni.ezquerra",
      "public_email": "",
      "name": "Jon Andoni Ezquerra",
      "state": "active",
      "locked": false,
      "avatar_url": "https://gitlab.com/uploads/-/system/user/avatar/22588622/avatar.png",
      "web_url": "https://gitlab.com/jonandoni.ezquerra"
    },
    "created_at": "2025-09-01T11:53:10.242Z",
    "updated_at": "2025-09-01T11:53:10.242Z",
    "system": false,
    "noteable_id": 411139248,
    "noteable_type": "MergeRequest",
    "project_id": 60982345,
    "commit_id": null,
    "position": {
      "base_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "start_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "head_sha": "8a6e3cec5cfa29f7051a2ff2251a54c340c218af",
      "old_path": "tests/V2/Application/QueryHandler/Admin/GetManagerCoursesHandlerTest.php",
      "new_path": "tests/V2/Application/QueryHandler/Admin/GetManagerCoursesHandlerTest.php",
      "position_type": "text",
      "old_line": null,
      "new_line": 60,
      "line_range": {
        "start": {
          "line_code": "94903149c23696e529ddb410158edd739cf5b250_0_59",
          "type": "new",
          "old_line": null,
          "new_line": 59
        },
        "end": {
          "line_code": "94903149c23696e529ddb410158edd739cf5b250_0_60",
          "type": "new",
          "old_line": null,
          "new_line": 60
        }
      }
    },
    "resolvable": true,
    "resolved": false,
    "resolved_by": null,
    "resolved_at": null,
    "suggestions": [],
    "confidential": false,
    "internal": false,
    "imported": false,
    "imported_from": "none",
    "noteable_iid": 1203,
    "commands_changes": {}
  },
  {
    "id": 2721895472,
    "type": "DiffNote",
    "body": "Tampoco hay que crear tests por que si, e
n este caso, no hay ninguna lógica en la creación de la query, por lo que nos lo podríamos ahorrar.",
    "author": {
      "id": 22588622,
      "username": "jonandoni.ezquerra",
      "public_email": "",
      "name": "Jon Andoni Ezquerra",
      "state": "active",
      "locked": false,
      "avatar_url": "https://gitlab.com/uploads/-/system/user/avatar/22588622/avatar.png",
      "web_url": "https://gitlab.com/jonandoni.ezquerra"
    },
    "created_at": "2025-09-01T11:53:10.031Z",
    "updated_at": "2025-09-01T11:53:10.031Z",
    "system": false,
    "noteable_id": 411139248,
    "noteable_type": "MergeRequest",
    "project_id": 60982345,
    "commit_id": null,
    "position": {
      "base_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "start_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "head_sha": "8a6e3cec5cfa29f7051a2ff2251a54c340c218af",
      "old_path": "tests/V2/Application/Query/Admin/GetManagerCoursesTest.php",
      "new_path": "tests/V2/Application/Query/Admin/GetManagerCoursesTest.php",
      "position_type": "file"
    },
    "resolvable": true,
    "resolved": false,
    "resolved_by": null,
    "resolved_at": null,
    "suggestions": [],
    "confidential": false,
    "internal": false,
    "imported": false,
    "imported_from": "none",
    "noteable_iid": 1203,
    "commands_changes": {}
  },
  {
    "id": 2721895466,
    "type": "DiffNote",
    "body": "Falta testar el forbidden, un usuario log
geado que no tenga los permisos adecuados y el de la validación del id en la uri.",
    "author": {
      "id": 22588622,
      "username": "jonandoni.ezquerra",
      "public_email": "",
      "name": "Jon Andoni Ezquerra",
      "state": "active",
      "locked": false,
      "avatar_url": "https://gitlab.com/uploads/-/system/user/avatar/22588622/avatar.png",
      "web_url": "https://gitlab.com/jonandoni.ezquerra"
    },
    "created_at": "2025-09-01T11:53:09.859Z",
    "updated_at": "2025-09-01T11:53:09.859Z",
    "system": false,
    "noteable_id": 411139248,
    "noteable_type": "MergeRequest",
    "project_id": 60982345,
    "commit_id": null,
    "position": {
      "base_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "start_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "head_sha": "8a6e3cec5cfa29f7051a2ff2251a54c340c218af",
      "old_path": "tests/Functional/V2/Admin/Manager/GetManagerCoursesFunctionalTest.php",
      "new_path": "tests/Functional/V2/Admin/Manager/GetManagerCoursesFunctionalTest.php",
      "position_type": "file"
    },
    "resolvable": true,
    "resolved": false,
    "resolved_by": null,
    "resolved_at": null,
    "suggestions": [],
    "confidential": false,
    "internal": false,
    "imported": false,
    "imported_from": "none",
    "noteable_iid": 1203,
    "commands_changes": {}
  },
  {
    "id": 2721895449,
    "type": "DiffNote",
    "body": "Quitar | final",
    "author": {
      "id": 22588622,
      "username": "jonandoni.ezquerra",
      "public_email": "",
      "name": "Jon Andoni Ezquerra",
      "state": "active",
      "locked": false,
      "avatar_url": "https://gitlab.com/uploads/-/system/user/avatar/22588622/avatar.png",
      "web_url": "https://gitlab.com/jonandoni.ezquerra"
    },
    "created_at": "2025-09-01T11:53:09.680Z",
    "updated_at": "2025-09-01T11:53:09.680Z",
    "system": false,
    "noteable_id": 411139248,
    "noteable_type": "MergeRequest",
    "project_id": 60982345,
    "commit_id": null,
    "position": {
      "base_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "start_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "head_sha": "8a6e3cec5cfa29f7051a2ff2251a54c340c218af",
      "old_path": "src/V2/Application/QueryHandler/Admin/GetManagerCoursesHandler.php",
      "new_path": "src/V2/Application/QueryHandler/Admin/GetManagerCoursesHandler.php",
      "position_type": "text",
      "old_line": null,
      "new_line": 29,
      "line_range": {
        "start": {
          "line_code": "e6ae39ac81a418f3dd0644bd1c47703be5fda1af_0_29",
          "type": "new",
          "old_line": null,
          "new_line": 29
        },
        "end": {
          "line_code": "e6ae39ac81a418f3dd0644bd1c47703be5fda1af_0_29",
          "type": "new",
          "old_line": null,
          "new_line": 29
        }
      }
    },
    "resolvable": true,
    "resolved": false,
    "resolved_by": null,
    "resolved_at": null,
    "suggestions": [],
    "confidential": false,
    "internal": false,
    "imported": false,
    "imported_from": "none",
    "noteable_iid": 1203,
    "commands_changes": {}
  },
  {
    "id": 2721895437,
    "type": "DiffNote",
    "body": "Esto no es un ejemplo válido",
    "author": {
      "id": 22588622,
      "username": "jonandoni.ezquerra",
      "public_email": "",
      "name": "Jon Andoni Ezquerra",
      "state": "active",
      "locked": false,
      "avatar_url": "https://gitlab.com/uploads/-/system/user/avatar/22588622/avatar.png",
      "web_url": "https://gitlab.com/jonandoni.ezquerra"
    },
    "created_at": "2025-09-01T11:53:09.368Z",
    "updated_at": "2025-09-01T11:53:09.368Z",
    "system": false,
    "noteable_id": 411139248,
    "noteable_type": "MergeRequest",
    "project_id": 60982345,
    "commit_id": null,
    "position": {
      "base_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "start_sha": "07945fab47de5fd0523ea979a7385b0214147cc4",
      "head_sha": "8a6e3cec5cfa29f7051a2ff2251a54c340c218af",
      "old_path": "openapi/v2/admin/openapi.yaml",
      "new_path": "openapi/v2/admin/openapi.yaml",
      "position_type": "text",
      "old_line": null,
      "new_line": 2106,
      "line_range": {
        "start": {
          "line_code": "76f5269d7c91754f6c8fc652fe1b2bfc28d586be_2064_2103",
          "type": "new",
          "old_line": null,
          "new_line": 2103
        },
        "end": {
          "line_code": "76f5269d7c91754f6c8fc652fe1b2bfc28d586be_2064_2106",
          "type": "new",
          "old_line": null,
          "new_line": 2106
        }
      }
    },
    "resolvable": true,
    "resolved": false,
    "resolved_by": null,
    "resolved_at": null,
    "suggestions": [],
    "confidential": false,
    "internal": false,
    "imported": false,
    "imported_from": "none",
    "noteable_iid": 1203,
    "commands_changes": {}
  },
  {
    "id": 2721833769,
    "type": null,
    "body": "marked this merge request as **ready**",
    "author": {
      "id": 22587919,
      "username": "roberto.fernandez2",
      "public_email": "",
      "name": "Roberto Fernández",
      "state": "active",
      "locked": false,
      "avatar_url": "https://gitlab.com/uploads/-/system/user/avatar/22587919/avatar.png",
      "web_url": "https://gitlab.com/roberto.fernandez2"
    },
    "created_at": "2025-09-01T11:28:01.044Z",
    "updated_at": "2025-09-01T11:28:01.049Z",
    "system": true,
    "noteable_id": 411139248,
    "noteable_type": "MergeRequest",
    "project_id": 60982345,
    "resolvable": false,
    "confidential": false,
    "internal": false,
    "imported": false,
    "imported_from": "none",
    "noteable_iid": 1203,
    "commands_changes": {}
  },
  {
    "id": 2721828699,
    "type": null,
    "body": "added 16 commits\n\n\u003cul\u003e\u003cl
i\u003e74ec297a...07945fab - 15 commits from branch \u
003ccode\u003edev\u003c/code\u003e\u003c/li\u003e\u003
cli\u003e8a6e3cec - [769] Create get manager course en
dpoint\u003c/li\u003e\u003c/ul\u003e\n\n[Compare with
previous version](/ManagementDrives/developers/learnfl
ix/learnflix/-/merge_requests/1203/diffs?diff_id=14764
89695\u0026start_sha=74ec297ad1b173637263f9b349699491ec50b479)",
    "author": {
      "id": 22587919,
      "username": "roberto.fernandez2",
      "public_email": "",
      "name": "Roberto Fernández",
      "state": "active",
      "locked": false,
      "avatar_url": "https://gitlab.com/uploads/-/system/user/avatar/22587919/avatar.png",
      "web_url": "https://gitlab.com/roberto.fernandez2"
    },
    "created_at": "2025-09-01T11:25:18.230Z",
    "updated_at": "2025-09-01T11:25:18.236Z",
    "system": true,
    "noteable_id": 411139248,
    "noteable_type": "MergeRequest",
    "project_id": 60982345,
    "resolvable": false,
    "confidential": false,
    "internal": false,
    "imported": false,
    "imported_from": "none",
    "noteable_iid": 1203,
    "commands_changes": {}
  },
  {
    "id": 2718442981,
    "type": null,
    "body": "assigned to @roberto.fernandez2",
    "author": {
      "id": 22587919,
      "username": "roberto.fernandez2",
      "public_email": "",
      "name": "Roberto Fernández",
      "state": "active",
      "locked": false,
      "avatar_url": "https://gitlab.com/uploads/-/system/user/avatar/22587919/avatar.png",
      "web_url": "https://gitlab.com/roberto.fernandez2"
    },
    "created_at": "2025-08-29T08:43:05.493Z",
    "updated_at": "2025-08-29T08:43:05.500Z",
    "system": true,
    "noteable_id": 411139248,
    "noteable_type": "MergeRequest",
    "project_id": 60982345,
    "resolvable": false,
    "confidential": false,
    "internal": false,
    "imported": false,
    "imported_from": "none",
    "noteable_iid": 1203,
    "commands_changes": {}
  }
]