import { defineStore } from 'pinia'
import { ref } from 'vue'
import ApiService from '@/core/services/api.service.js'
import { USER_API_ROUTES } from '@/contexts/users/constants/users.constants.js'
import { toast } from 'vue3-toastify'
import { UserFormModel } from '@/contexts/users/models/userForm.model.js'
import { useI18n } from 'vue-i18n'
import { UserFormExtraFieldModel } from '@/contexts/users/models/userFormExtraField.model.js'

export const userFormStore = defineStore('userFormStore', () => {
  const userData = ref(new UserFormModel())
  const i18n = useI18n()
  const isLoading = ref(false)
  const loadingError = ref(false)
  async function loadUserData(id = 0) {
    loadingError.value = false
    isLoading.value = true
    if (!id) {
      userData.value = new UserFormModel()
      isLoading.value = false
      return null
    }
    const parsedUrl = ApiService.setParams(USER_API_ROUTES.FORM.INIT_DATA, { id })
    const { error, data } = await ApiService.get(parsedUrl)
    isLoading.value = false
    if (error) {
      loadingError.value = true
      return toast.error(error.message)
    }
    userData.value = new UserFormModel(data)
  }

  const successSave = ref(false)
  async function createUser(payload = {}) {
    successSave.value = false
    const { error } = await ApiService.post(USER_API_ROUTES.FORM.NEW_USER, payload)
    if (error) {
      return toast.error(error.message)
    }
    toast.success(i18n.t('CATALOG.SAVED'))
    successSave.value = true
  }

  async function updateUser(id = 0, payload = {}) {
    successSave.value = false
    const parsedUrl = ApiService.setParams(USER_API_ROUTES.FORM.UPDATE_USER, { id })
    const { error } = await ApiService.post(parsedUrl, payload)
    if (error) {
      return toast.error(error.message)
    }
    toast.success(i18n.t('CATALOG.SAVED'))
    successSave.value = true
  }

  const extraData = ref([])
  async function loadExtraData() {
    extraData.value = []
    const { error, data } = await ApiService.get(USER_API_ROUTES.FORM.EXTRA_FIELDS)
    if (!error) {
      extraData.value = data.map((field) => new UserFormExtraFieldModel(field))
    }
  }

  return {
    userData,
    successSave,
    extraData,
    isLoading,
    loadingError,
    loadUserData,
    createUser,
    updateUser,
    loadExtraData,
  }
})
