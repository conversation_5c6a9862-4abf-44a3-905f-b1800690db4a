import { make } from "vuex-pathify";
import axios from "axios";

const getDefaultState = () => ({
  loading: true,
  companies: [],
});

const state = () => getDefaultState();

export const getters = {
  getCompanies: (state) => () => state.companies,
};

export const mutations = {
  ...make.mutations(state),

  SET_NEW_VALUE_COMPANY(state, newValue) {
    state.companies = state.companies.map((company) => {
      if (company.id === newValue.id) {
        return newValue;
      }

      return company;
    });
  },

};

export const actions = {
  ...make.actions(state),

  async load({ commit }, endpoint) {
    try {
      commit("SET_LOADING", true);
      const { data, error } = await axios.get(endpoint);

      if (error) {
        throw new Error(data?.message);
      }

      commit("SET_COMPANIES", data.data);

    } catch (error) {
      console.log(error); 
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async changeActiveState({ commit }, { endpoint, requestData}) {
    try {
      commit("SET_LOADING", true);

      const { data, error } = await axios.put(endpoint, requestData);

      if (error) {
        throw new Error(data?.message);
      }
      commit("SET_NEW_VALUE_COMPANY", data?.data);

    } catch (error) {
      console.log(error);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async save({ commit }, { endpoint, requestData }) {
    try {
      commit("SET_LOADING", true);

      const { data, error } = await axios.post(endpoint, requestData);

      commit("SET_NEW_VALUE_COMPANY", data?.data);

      if (error) {
        throw new Error(data?.message);
      }
    } catch (error) {
      console.log(error);
    } finally {
      commit("SET_LOADING", false);
    }
  },
  async deleteCompany({ commit }, { endpoint }) {
    try {
      commit("SET_LOADING", true);
      const { data, error } = await axios.post(endpoint);
      if (error) {
        throw new Error(data?.message);
      }
    } catch (error) {
      console.log(error);
    } finally {
      commit("SET_LOADING", false);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
