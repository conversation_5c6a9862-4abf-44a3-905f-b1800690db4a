import HomeView from "../views/HomeView.vue";
import Create<PERSON>ur<PERSON> from "../views/CreateSurvey.vue";
import ViewSurvey from "../views/ViewSurvey.vue";
import SurveyOpinions from "../components/SurveyOpinions.vue";

export default [
    {
        path: '/admin/apps/survey',
        component: HomeView,
        name: 'Home'
    },
    {
        path: '/admin/apps/survey/create',
        component: <PERSON><PERSON><PERSON><PERSON><PERSON>,
        name: 'C<PERSON><PERSON>urvey'
    },
    {
        path: '/admin/apps/survey/:id/update',
        component: <PERSON><PERSON><PERSON>urvey,
        name: 'UpdateSurvey'
    },
    {
        path: '/admin/apps/survey/:id/opinions',
        component: SurveyOpinions,
        name: 'ViewSurveyOpinions'
    }
];
