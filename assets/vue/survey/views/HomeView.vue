<template>
  <home
    title="SURVEY.HOME.TITLE"
    description="SURVEY.HOME.DESCRIPTION"
    src-thumbnail="/assets/imgs/survey_app.svg"
  >
    <template v-slot:content-actions>
      <router-link
        :to="{ name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', params: {} }"
        class="btn btn-primary"
        >{{ $t("SURVEY.ADD_SURVEY") }}</router-link
      >
    </template>
    <template v-slot:content-main>
      <div
        class="d-flex align-items-center justify-content-center"
        v-if="loading"
      >
        <spinner />
      </div>
      <div v-else>
        <table class="table">
          <thead>
            <tr>
              <th>{{ $t("NAME") }}</th>
              <th class="text-center">{{ $t("QUESTIONS") }}</th>
              <th>{{ $t("CREATED_BY") }}</th>
              <th class="text-center">{{ $t("ACTIVE") }}</th>
              <th class="text-center">
                <LabelWithInfo
                  :info="
                    $t('SURVEY.HOME.INITIAL_INFO')
                      + ''
                  "
                  id="initial-info"
                  location="top"
                >
                  {{ $t("COURSE.COURSE_SECTION.INITIAL") }}
                </LabelWithInfo>
              </th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="survey in surveys" :key="survey.id">
              <td>{{ survey.name }}</td>
              <td class="text-center">
                <span class="badge badge-primary ps-2 pe-2">{{
                  survey.totalQuestions
                }}</span>
              </td>
              <td>{{ survey.firstName }} {{ survey.lastName }}</td>
              <td class="text-center">
                <div class="custom-control custom-switch">
                  <input
                    type="checkbox"
                    class="custom-control-input"
                    :id="'switch_survey_' + survey.id"
                    v-model="survey.active"
                    :disabled="survey.isMain"
                    @change="activateSurvey(survey)"
                  />
                  <label
                    class="custom-control-label"
                    :for="'switch_survey_' + survey.id"
                  ></label>
                </div>
              </td>
              <td class="text-left">
                <button
                  @click="changeIsMain(survey)"
                  type="button"
                  style="border: none; background-color: transparent"
                >
                  <i v-if="survey.isMain" class="fas fa-flag text-primary"></i>
                  <i v-else class="fas fa-flag" style="color: #cbd5e1"></i>
                </button>
              </td>
              <td style="text-align: right">
                <div class="dropdown">
                  <button
                    class="btn btn-default"
                    type="button"
                    :id="`dropdown-menu-${survey.id}`"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                  >
                    <i class="fa fa-ellipsis-h"></i>
                  </button>
                  <ul
                    class="dropdown-menu"
                    :aria-labelledby="`dropdown-menu-${survey.id}`"
                  >
                    <li>
                      <router-link
                        :to="{
                          name: 'UpdateSurvey',
                          params: { id: survey.id },
                        }"
                        class="dropdown-item"
                        >{{ $t("EDIT") }}</router-link
                      >
                    </li>
                    <!--   <li><router-link :to="{name: 'ViewSurveyOpinions', params: {id: survey.id}}" class="dropdown-item">{{ $t('VIEW') }}</router-link></li>
                <li><a :href="`/admin/survey/${survey.id}/preview`" target="_blank"
                        class="dropdown-item">{{ $t('CATALOG.TYPE_DIPLOMA.PREVIEW') }}</a></li> -->
                   <li>
                      <a class="dropdown-item" @click="cloneSurvey(survey)">{{
                        $t("CLONE")
                      }}</a>
                    </li>
                    <li>
                      <a
                        class="dropdown-item delete"
                        @click="deleteSurvey(survey)"
                        >{{ $t("DELETE") }}</a
                      >
                    </li>
                  </ul>
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="5" v-if="surveys.length === 0">
                <base-not-result />
              </td>
            </tr>
          </tbody>
        </table>
        <pagination
          :prop-current-page="page"
          :total-items="totalItems"
          @current-page="onCurrentPage"
        />
      </div>
    </template>
  </home>
</template>

<script>
import { get, sync } from "vuex-pathify";
import Home from "../../base/Home.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import BaseNotResult from "../../base/BaseNotResult.vue";
import Pagination from "../../admin/components/Pagination.vue";
import LabelWithInfo from "../../common/components/ui/LabelWithInfo.vue";

export default {
  name: "HomeView",
  components: { Pagination, BaseNotResult, Spinner, Home, LabelWithInfo },
  data() {
    return {};
  },
  computed: {
    ...get("surveyModule"),
    surveys: get("surveyModule/surveys"),
    loading: sync("surveyModule/loading"),
    page: sync("surveyModule/pagination@page"),
    totalItems: get("surveyModule/pagination@totalItems"),
  },
  created() {
    this.$store.dispatch("contentTitleModule/addRoute", {
      routeName: this.$route.name,
      params: {
        linkName: this.$t("SURVEY.HOME.TITLE"),
        params: {},
      },
    });

    this.loading = true;
    this.getSurveys();
  },
  methods: {
    onCurrentPage(page) {
      this.page = page;
      this.getSurveys();
    },
    getSurveys() {
      this.$store.dispatch("surveyModule/getSurveys");
    },

    activateSurvey(survey) {
      this.$store
        .dispatch("surveyModule/activateSurvey", {
          id: survey.id,
          active: survey.active,
        })
        .then((res) => {
          const { error } = res;
          if (error) this.$toast.error(this.$t("SURVEY.ACTIVATE.FAILED") + "");
        });
    },

    cloneSurvey(survey) {
      this.$alertify.confirmWithTitle(
        this.$t("SURVEY.CLONE.CONFIRM.TITLE"),
        this.$t("SURVEY.CLONE.CONFIRM.DESCRIPTION"),
        () => {
          this.$store
            .dispatch("surveyModule/cloneSurvey", survey.id)
            .then((res) => {
              const { error } = res;
              if (error) this.$toast.error(this.$t("SURVEY.CLONE.FAILED") + "");
              else this.$toast.success(this.$t("SURVEY.CLONE.SUCCESS") + "");
            });
        },
        () => {}
      );
    },

    deleteSurvey(survey) {
      if (survey.isMain) {
       return  this.$toast.error(
          this.$t("COURSE.COURSE_SECTION.ISMAIN_ERROR1", {
            section: this.$t("CHAPTER.CONTENT.SECTION").toLowerCase(),
          }) + ""
        );
      }

      this.$alertify.confirmWithTitle(
        this.$t("SURVEY.DELETE.CONFIRM.TITLE"),
        this.$t("SURVEY.DELETE.CONFIRM.DESCRIPTION"),
        () => {
          this.$store
            .dispatch("surveyModule/deleteSurvey", survey.id)
            .then(res => { 
              const { error, status } = res;
              if (error)
                  this.$toast.error(this.$t("SURVEY.DELETE.FAILED") + "");
              else this.$toast.success(this.$t("SURVEY.DELETE.SUCCESS") + "");
            }).catch(e => {
              if(e==409) this.$toast.error((this.$t('SURVEY.DELETE.ERROR_MESSAGE') + ''));
              else this.$toast.error(e);
            })
        },
        () => {}
      );
    },
    
    async changeIsMain(survey) {
      if (survey.isMain) {
        this.$toast.error(
          this.$t("COURSE.COURSE_SECTION.ISMAIN_ERROR1", {
            section: this.$t("ANNOUNCEMENT.FORM.STEPS.SURVEY").toLowerCase(),
          }) + ""
        );
      } else if (!survey.active) {
        this.$toast.error(
          this.$t("COURSE.COURSE_SECTION.ISMAIN_ERROR2", {
            section: this.$t("ANNOUNCEMENT.FORM.STEPS.SURVEY").toLowerCase(),
          }) + ""
        );
      } else {
        await this.$store.dispatch("surveyModule/changeIsMain", {
          surveyId: survey.id,
        });
        this.getSurveys();
      }
    },
  },
};
</script>

 <style scoped lang="scss"> 
.Home {
  :deep(.Home--header--banner) {
    height: 190px !important;
  }
  :deep(.Home--header) {
    background-color: #f6f7f8;
  }
  :deep(.Home--content) {
    background-color: #ffffff;
  }

  :deep(.Home--content--main),
  :deep(.Home--content--actions) {
    padding: 1rem 2rem !important;
  }

  .badge-primary {
    background-color: rgb(181, 180, 180) !important;
  }
}
</style>
