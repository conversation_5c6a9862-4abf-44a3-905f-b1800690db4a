export const STATUS_PENDING = 0;
export const STATUS_IN_PROGRESS = 1;
export const STATUS_COMPLETED = 2;
export const STATUS_FAILED = -1;

export const ZIP_FILE_TASK_STATUS = {
    PENDING: STATUS_PENDING,
    IN_PROGRESS: STATUS_IN_PROGRESS,
    COMPLETED: STATUS_COMPLETED,
    FAILED: STATUS_FAILED
};

export default {
    methods: {
        zipIsPending(status) {
            return status === STATUS_PENDING;
        },
        zipIsInProgress(status) {
            return status === STATUS_IN_PROGRESS;
        },
        zipIsCompleted(status) {
            return status === STATUS_COMPLETED;
        },
        zipIsFailed(status) {
            return status === STATUS_FAILED;
        }
    }
}
