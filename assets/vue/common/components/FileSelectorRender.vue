<template>
  <div class="FileSelectorRender" :id="id" :style="{
    'width': renderWidth,
    'height': renderHeight
  }">
    <canvas  :id="`${id}-canvas`"></canvas>
    <div class="VideoControls" v-if="isVideo">
      <button @click.stop="play()"><i class="fa fa-play"></i></button>
      <button @click.stop="pause()"><i class="fa fa-pause"></i></button>
      <button @click.stop="mute()"><i class="fa fa-volume-mute"></i></button>
      <button @click.stop="unmute()"><i class="fa fa-volume-up"></i></button>
    </div>
  </div>
</template>

<script>
const pdfJsLib = require('pdfjs-dist/build/pdf');
pdfJsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js';

export const A4_WIDTH = 695;
export const A4_HEIGHT = 842;

export default {
  name: "FileSelectorRender",
  props: {
    id: {
      type: String,
      default: 'file-selector-renderer'
    },
    accept: {
      type: String,
      required: true
    },
    file: {
      type: File,
      required: true
    },
    width: {
      type: Number,
      required: true
    },
    height: {
      type: Number,
      required: true
    },
    isPercent: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      newHeight: 0,
      newWidth: 0,
      memoryData: {
        urlObject: null,
        object: null
      }
    };
  },
  computed: {
    renderHeight() {
      return this.file.type.includes('pdf') ? this.newHeight + 'px' : this.height + (this.isPercent ? '%' : 'px');
    },
    renderWidth() {
      if (this.accept.includes('video')) {
        return this.newWidth + 'px';
      }
      return this.width + (this.isPercent ? '%' : 'px');
    },
    isVideo() {
      return this.accept.includes('video');
    }
  },
  watch: {
    file: {
      handler: function (val, oldVal) {
        this.renderFile(this.file);
      },
      deep: true
    }
  },
  mounted() {
    this.renderFile(this.file);
  },

  beforeDestroy() {
    if (this.memoryData.urlObject != null) URL.revokeObjectURL(this.memoryData.urlObject);// Invalidate created url object for memory cleaning
    if (this.memoryData.object) this.memoryData.object.remove();// Is a dom element, remove for memory cleaning
  },

  methods: {
    calculateHeight() {
      if (this.accept.includes('pdf')) {
        const elementWidth = document.getElementById(this.id).clientWidth;
        this.newHeight = (A4_HEIGHT / A4_WIDTH) * elementWidth;
      }
    },

    renderFile(file) {
      if (this.memoryData.urlObject != null) URL.revokeObjectURL(this.memoryData.urlObject);// Invalidate created url object for memory cleaning
      if (this.memoryData.object) this.memoryData.object.remove();// Is a dom element, remove for memory cleaning

      this.calculateHeight(file);
      if (this.accept.includes('video')) {
        this.renderVideoFile(file);
      } else {
        this.readFileAsync(file).then(result => {
          if (file.type.includes('pdf')) this.renderPdfContent(result)
          if (file.type.includes('image')) this.renderImageContent(result);
        })
      }
    },

    renderVideoFile(file) {
      const self = this;
      let canvas = document.getElementById(this.id + '-canvas');
      let context = canvas.getContext('2d');

      const video = document.createElement('video');
      self.memoryData = {
        urlObject: URL.createObjectURL(file)
      };
      video.src = self.memoryData.urlObject;
      video.muted = true;

      video.oncanplay = function (e) {
        video.play();
        video.pause();
        requestAnimationFrame(updateCanvas);
      };

      //this.newHeight = (A4_HEIGHT / A4_WIDTH) * elementWidth;
      const clientHeight = document.getElementById(this.id).clientHeight;

      video.addEventListener('loadedmetadata', function () {
        console.log('loaded metadata');
        console.log(video.videoWidth);
        console.log(video.videoHeight);
        console.log('Element height ' + clientHeight);
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        self.newWidth = (video.videoWidth / video.videoHeight) * clientHeight;
        // console.log(newWidth);
      })

      self.memoryData.object = video;

      function updateCanvas() {
        context.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
        window.requestAnimationFrame(updateCanvas);
      }
    },

    renderImageContent(result) {
      let canvas = document.getElementById(this.id + '-canvas');
      let context = canvas.getContext('2d');
      let image = new Image();
      image.onload = function () {
        canvas.width = image.naturalWidth;
        canvas.height = image.naturalHeight;
        context.drawImage(image, 0, 0);
      }
      image.src = result;
    },

    renderPdfContent(result) {
      let canvas = document.getElementById(this.id + '-canvas');
      let context = canvas.getContext('2d');
      let scale = 1;

      let loadingTask = pdfJsLib.getDocument({data: result});
      loadingTask.promise.then(function (pdf) {
        pdf.getPage(1).then(function(page) {
          let viewport = page.getViewport({scale: scale});
          canvas.height = viewport.height;
          canvas.width = viewport.width;

          let renderContext = {
            canvasContext: context,
            viewport: viewport
          };

          let renderTask = page.render(renderContext);
          renderTask.promise.then(function () {
            // console.log('rendered')
          })
        });
      });
    },

    readFileAsync(file) {
      return new Promise((resolve, reject) => {
        let reader = new FileReader();
        reader.onload = e => {
          resolve(reader.result);
        };
        reader.onerror = reject;
        if (file.type.includes('image')) reader.readAsDataURL(file);
        else
          reader.readAsBinaryString(file);
      });
    },

    play() {
      this.memoryData.object.play();
    },

    pause() {
      this.memoryData.object.pause();
    },

    mute() {
      this.memoryData.object.muted = true;
    },

    unmute() {
      this.memoryData.object.muted = false;
    }
  }
}
</script>

 <style scoped lang="scss"> 
.FileSelectorRender {
  position: relative;
  canvas {
    width: 100%;
    height: 100%;
  }

  .VideoControls {
    position: absolute;
    bottom: 0;
    width: 100%;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: center;

    button {
      border: none;
      color: #212121;
      font-size: 20px;
      background-color: #ffffff;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin: 0.1rem;
      &:hover {
        border: none;
        transform: scale(1.1);
        transition: all .2s ease-in;
      }
    }
  }
}
</style>
