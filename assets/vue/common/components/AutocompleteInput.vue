<script>
export default {
  name: "AutocompleteInput",
  props: {
    value: null,
    suggestions: {
      type: Object|Array,
      default: ([])
    }
  },
  data() {
    return {
      open: false,
      current: 0
    };
  },
  computed: {
    innerValue: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit('input', newValue);
      }
    },
    openSuggestion() {
      return this.innerValue !== '' && this.matches.length !== 0 && this.open === true;
    },
    matches() {
      return this.suggestions.filter((str) => {
        return str.indexOf(this.innerValue) >= 0;
      });
    }
  },
  methods: {
    onSelect(index) {
      this.innerValue = this.matches[index];
      this.open = false;
    },
    enter() {
      this.innerValue = this.matches[this.current];
      this.open = false;
    },
    change() {
      if (false === this.open) {
        this.open = true;
        this.current = 0;
      }
    },

    up() {
      if (this.current > 0) {
        this.current--;
      }
    },

    down() {
      if (this.current < this.matches.length - 1) {
        this.current++;
      }
    }
  }
}
</script>

<template>
  <div class="w-100 position-relative" :class="openSuggestion ? 'open' : ''">
    <input type="text" class="form-control" v-model="innerValue"
           @input="change()"
           @focus="change()"
           @keydown.enter="enter"
           @keydown.up="up"
           @keydown.down="down"
    >
    <ul class="dropdown-menu auto-complete-input w-100" :class="open ? 'd-block' : 'd-none'">
      <li v-for="(s, i) in matches" @click="onSelect(i)" :class="i === current ? 'active' : ''">
        <a href="#">{{ s }}</a>
      </li>
    </ul>
  </div>
</template>

<style scoped lang="scss">
.dropdown-menu {
  li {
    padding: 0 5px;
    &.active {
      background-color: #ffffff !important;
      border: 1px solid var(--color-primary) !important;
      color: var(--color-primary) !important;
    }
  }
}
</style>
