<script>
import Spinner from "../../../admin/components/base/Spinner.vue";
import {get, sync} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";

export default {
  name: "TypeCourse",
  components: {BaseSwitch, Spinner},
  computed: {
    loading: get('catalogModule/loading'),
    catalogs: sync('catalogModule/catalogs'),
  },

  created() { 
    this.$store.dispatch('catalogModule/load', '/admin/type-course/all');
  },
  methods: {
    changeActiveStatus(index) {
      const value = this.catalogs[index];
      this.$store.dispatch('catalogModule/changeActiveState',
          {endpoint: `/admin/type-course/${value.id}/active`, data: {id: value.id, active: value.active}}).then(r => {

      });
    }
  }
}
</script>

<template>
  <div>
    <div class="col-12 d-flex flex-row align-items-center justify-content-center" v-if="loading">
      <spinner />
    </div>
    <table class="table table-condensed mt-3" v-else>
      <thead>
      <tr>
        <th></th>
        <th>{{ $t('NAME') }}</th>
        <th>{{ $t('ACTIVE') }}</th>
        <th>{{ $t('TIPO') }}</th>
        <th></th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(c, index) in catalogs" :key="c.id">
        <td></td>
        <td>{{ c.name }}</td>
        <td>
          <BaseSwitch :tag="`switcher-type-course-${c.id}`" v-model="c.active" @change="changeActiveStatus(index)" />
        </td>
        <td>{{ c.denomitation }}</td>
        <td>
          <div class="dropdown">
            <button class="btn btn-default" type="button" :id="`dropdown-menu-${c.id}`" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fa fa-ellipsis-h"></i>
            </button>
            <ul class="dropdown-menu" :aria-labelledby="`dropdown-menu-${c.id}`">
              <li><router-link class="dropdown-item" :to="{ name: 'TypeCourseUpdate', params: {...$route.params, id: c.id} }">{{ $t('EDIT') }}</router-link></li>
            </ul>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped lang="scss">

</style>
