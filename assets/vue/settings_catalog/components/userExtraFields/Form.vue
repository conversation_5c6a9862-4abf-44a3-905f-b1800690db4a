<template>
  <div class="FormView" >
    <div
      class="tab-pane fade show active"
      id="divFicha"
      role="tabpanel"
      aria-labelledby="divFicha-tab"
    >
      <div class="form-group col-12" >
        <label>{{ $t('TYPE') }}</label>
        <div v-if="routeName == 'UserExtraFieldsCreate'">
          <select id="type" v-model="selected" class="form-control" @change="typeChance($event)">
            <option v-for="option in options" v-bind:value="option.value">
              {{ option.text }}
            </option>
          </select>
        </div>
        <div v-else>
          <input type="text" class="form-control" v-model="userExtraField.type" :disabled="disabled">
        </div> 
      </div>  

      <div class="m-4">
        <Translation
          v-model="locale"
          v-for="t in userExtraField.label.translations"
          v-if="t.language === locale"
        >
          <template v-slot:content>
            <div class="form-group col-12 required">
              <label>{{ $t("USER.EXTRA_FIELDS.LABEL") }}</label>
              <input type="text" class="form-control" v-model="t.value" />
            </div>
          </template>
        </Translation>
      </div>

      <div class="form-group col-12 d-flex align-items-center justify-content-start">
        <BaseSwitch :tag="`switcher-userExtraFields-required-${element}`"
                    v-model="userExtraField.required"/>
        <label class="ml-1">{{ $t('USER.EXTRA_FIELDS.REQUIRED') }}</label>
      </div>  
    </div>

    <div class="Options" v-if="typeMultiple">
      <div class="Options--header col-12 d-flex align-items-center justify-content-end">
        <h3 class="mr-auto">{{ $t('USER.EXTRA_FIELDS.OPTIONS.TITLE') }}</h3>
        <button @click="addOption()" type="button" class="btn btn-primary">
          <i class="fa fa-plus"></i> {{ $t('USER.EXTRA_FIELDS.OPTIONS.ADD_OPTION') }}
        </button>
      </div>  
    
      <div>
        <table class="table table-condensed mt-3" >
          <thead>
          <tr>
            <th></th>
            <th>{{ $t('NAME') }}</th>
            <th></th>
          </tr>
          </thead>
          <tbody>
            <tr v-for="(c, index) in userExtraField.options" :key="index">
              <td></td>
              <td>{{ c.name.default }}</td>
              <td class="text-right">
                <button type="button" class="btn btn-sm btn-primary" 
                  @click="updateOption(index)"><i class="fa fa-pencil"></i>
                </button>
                <button type="button" class="btn btn-sm btn-danger" 
                  @click="deleteOption(index)" ><i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div> 

    <div class="modal fade" id="new-option-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="new-option-modal" aria-hidden="true">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="btn-close" @click="closeModal()" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="m-4">
              <Translation
                v-model="locale"
                v-for="t in optionEdit.name.translations"
                v-if="t.language === locale"
              >
                <template v-slot:content>
                  <div class="form-group col-12 required">
                    <label>{{ $t("USER.EXTRA_FIELDS.LABEL") }}</label>
                    <input type="text" class="form-control" v-model="t.value" />
                  </div>
                </template>
              </Translation>
              <div class="m-4">
                <button type="button" class="btn btn-sm btn-primary"  @click="saveOption()">
                  {{ $t("SAVE") }}
                </button>
                <button type="button" class="btn btn-sm btn-danger"  @click="closeModal()" >
                  {{ $t("CANCEL") }}
                </button>                
              </div>
            </div>
          </div>
        </div>
      </div> 
    </div>

    <div class="col-12 d-flex align-items-center justify-content-end">
      <button type="submit" class="btn btn-primary" @click="submit()">
        {{ $t("SAVE") }}
      </button> 
    </div>
  </div>
</template>

<script>
import BaseForm from "../BaseForm.vue";
import {get} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";
import Translation from "../../../common/components/Translation.vue";
import $ from "jquery";

export default {
  name: "Form",
  components: {BaseSwitch, BaseForm, Translation},
  data() {
    return {
      userExtraField: [],
      element: 0,
      locale: 'es',
      routeName: 'UserExtraFieldsCreate',
      selected: 0,
      options: [
        { text: "Text", value: 0 },
        { text: "Select", value: 1 },
      ],
      disabled: false,
      typeMultiple: false,
      optionEdit:{},
      optionSelected:-1,
    };
  },
  computed: {
    userExtraFields: get('userExtraFieldsModule/userExtraFields'),
  },
  created() {
    const update = this.$route.name === 'UserExtraFieldsUpdate';
   // se está duplicando el título
    // this.$store.dispatch("contentTitleModule/addRoute", {
    //   routeName: this.$route.name,
    //   params: {
    //     linkName: update
    //       ? this.$t("USER.EXTRA_FIELDS.FORM.TITLE_UPDATE")
    //       : this.$t("USER.EXTRA_FIELDS.FORM.TITLE_CREATE"),
    //     params: {},
    //   },
    // });
    this.initFormParameters(update);
    this.initOptionEdit();
  },
  methods: {
    initFormParameters(update){
      if (update) {
        this.disabled = true;
        this.routeName = this.$route.name;
        this.element = this.$route.params.id;
        this.userExtraField = this.userExtraField1 = this.userExtraFields[this.element];
        if(this.userExtraField.type === "select"){
          this.typeMultiple = true;
        }
        if (this.userExtraField === undefined) {
          this.returnToList();
          return;
        }
      }else {
        this.element = this.userExtraFields.length;
        this.initUserExtraFieldElement();
      }
    },
    returnToList() {
      this.$router.push({name: 'UserExtraFields', params: this.$route.params});
    },
    addOption(){
      this.initOptionEdit();
      if(this.userExtraField.options)
        this.optionEdit.value = this.optionSelected = this.userExtraField.options.length;
      else{
        this.userExtraField.options= [];
        this.optionSelected = 0;
        this.optionEdit.value = 1;
      }
      this.openModal();
    },
    updateOption(index){
      this.optionSelected = index;
      this.optionEdit = this.userExtraField.options[index];
      this.openModal();
    },
    saveOption(){
      this.optionEdit.name.default = this.optionEdit.name.translations[0].value;
      this.userExtraField.options[this.optionSelected] = this.optionEdit;
      //*** error: al cerrar la modal, no actualiza listado al insertar una nueva opción */
      this.closeModal();
    },
    deleteOption(index){
      this.userExtraField.options.splice(index, 1);
    },
    openModal() {
      $('#new-option-modal').modal({
        show  : true,
        static  : true,
        backdrop: false,
        keyboard: false
      });
    },
    closeModal() {
      this.questionsLocal = [];
      this.questionsLocal = this.innerValue;
      this.editQuestion = null;
      $('#new-option-modal').modal('hide');
    },
    async submit() {
      const update = this.$route.name === 'UserExtraFieldsUpdate';
      const continueVal = this.validateDefault(this.userExtraField.label.translations[0].value);

      if(continueVal){
        this.userExtraFields[this.element] = this.userExtraField;  
        await this.$store.dispatch('userExtraFieldsModule/changeSave')
        .then(response => {
          this.$toast.success(this.$t('SUCCESS_SAVE') + '');
          this.returnToList();
        }).catch((response) => {
          this.$toast.error(this.$t('SAVE_FAILED') + '');
        });

      }
    },
    validateDefault(transitionValue){
      if (!transitionValue){ 
        this.$toast.error(this.$t('USER.EXTRA_FIELDS.ERROR_DEFAULT') + '');
        return false;
      }else{ 
        this.userExtraField.label.default = this.userExtraField.label.translations[0].value;
        return true;
      }
    },
    typeChance(event){
      this.typeMultiple = event.target.value === 0 ? false: true;
      this.userExtraField.name = "input_" + this.options[event.target.value].text;
    },
    initUserExtraFieldElement(){
      this.userExtraField = {
        "name": "input_text",
        "required": true,
        "label":{
          "default": null,
          "translations":[
            {
              "language": "es",
              "value": null
            },
            {
              "language": "en",
              "value": null
            },
            {
              "language": "pt",
              "value": null
            },
            {
              "language": "fr",
              "value": null
            }
          ]
        },
        "type": "text"
      }; 
    },
    initOptionEdit(){
      this.optionEdit = {
        value:-1,
        name:{
          default: null,
          translations: [
            {
              "language": "es",
              "value": null
            },
            {
              "language": "en",
              "value": null
            },
            {
              "language": "pt",
              "value": null
            },
            {
              "language": "fr",
              "value": null
            }
          ],
        },        
      }
    },
  },
}

</script>

<style scoped lang="scss">
  label {
    text-transform: uppercase;
    font-weight: 500;
    padding: 0 .5rem;
    margin-top: .25rem;
  }

  .form-control {
    background-color: #FFFFFF;
    border: 1px solid #E5E5E5;
    border-radius: 50px;
  }
  .extra-field {
    padding: 1rem;
    margin-top: 5rem;

    &--table {
      overflow: auto;
    }
    &--header {
      h3 {
        font-size: 22px;
      }
    }
  }
</style>
