<template>
  <div class="visorResources">
    <div v-if="origen == 'vimeo'">
      <vimeo-resource :id="id" :identifier="identifier" class="vimeo" :urlvideo="urlvideo" :widthvideo="widthvideo" :heightvideo="heightvideo"/>
    </div>

    <div v-else>
      <youtube-resource :id="id" :identifier="identifier"  class="youtube"/>
    </div>
  </div>
</template>

<script>
import YoutubeResource from "../components/resources-videos/youtube-resource";
import VimeoResource from "../components/resources-videos/vimeo-resource";

export default {
  name: "visor-resources",
  components: {
    YoutubeResource,
    VimeoResource,
  },
  props: ["id", "urlvideo", "origen", "identifier", "widthvideo", "heightvideo"],
};
</script>

 <style scoped lang="scss"> 
.visorResources {
  max-width:100%;
  height: 100%;
  margin: auto;
  background: black;
  .vimeo{
    margin:auto;
    width: 100%;
    background: black;
  }
  .youtube{
    background: black;
  }
}


</style>
