<template>
  <div class="Youtube">
    <div class="modal" v-show="showModal == 1">
      <img
        :src="`/assets/video/replay.svg`"
        class="iconsSvg"
        @click="reloadPage()"
      />

      <img
        :src="`/assets/video/cerrar.svg`"
        class="iconsSvg"
        @click="closeModal()"
      />
    </div>

    <div v-if="infoChapter != undefined && showModal == 0" class="video">
      <youtube
        :video-id="identifier"
        :player-vars="{
          autoplay: 1,
          start: timeVideo,
          showinfo: 0,
          rel: 0,
          fs: 0,
        }"
        :start="20"
        @playing="playing"
        @ended="ended"
        @paused="paused"
        width="100%"
        height="100%"
        ref="youtube"
      />
    </div>
    <div v-if="infoChapter == undefined">
      <spinner class="spinner" />
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import Spinner from "../base/Spinner";

export default {
  name: "youtube-resource",
  components: {
    Spinner,
  },
  props: ["id", "identifier"],

  data() {
    return {
      infoChapter: undefined,
      showModal: 0,
      timeVideo: 0,
    };
  },

  async created() {
    this.infoChapter = await this.$store.dispatch(
      "chapterModule/getState",
      this.id
    );
    this.timeVideo = this.infoChapter?.timeVideo ?? 0;

    if (this.infoChapter.finished) {
      this.timeVideo = 0;
    }

    await this.$store
      .dispatch("chapterModule/getState", this.id)
      .then(async (data) => {
        await this.$store.dispatch("timeModule/initStart", this.userChapter());

        const chapterStatus = {
          chapter: this.id,
          finished: false,
        };

        if (!this.infoChapter.finished) {
          await this.$store.dispatch(
            "chapterModule/updateState",
            chapterStatus
          );
        }
      });
  },

  mounted() {
    setInterval(() => {
      this.saveTime();
    }, 10000);
  },

  computed: {
    ...get("chapterModule", ["userChapter"]),
    player() {
      return this.$refs.youtube.player;
    },
  },

  methods: {
    async playing() {
      let time = await this.player.getCurrentTime();
      console.log(time);
      const chapterStatus = {
        chapter: this.id,
        finished: false,
        timeVideo: Math.round(time),
      };

      if (!this.infoChapter.finished) {
        await this.$store.dispatch("chapterModule/updateState", chapterStatus);
      }
    },

    async paused() {
      let time = await this.player.getCurrentTime();
      const chapterStatus = {
        chapter: this.id,
        finished: false,
        timeVideo: Math.round(time),
      };

      if (!this.infoChapter.finished) {
        await this.$store.dispatch("chapterModule/updateState", chapterStatus);
      }
    },

    async ended() {
      const chapterStatus = {
        chapter: this.id,
        finished: true,
      };

      await this.saveTime();
      await this.$store.dispatch("chapterModule/updateState", chapterStatus);
      this.infoChapter = await this.$store.dispatch(
        "chapterModule/getState",
        this.id
      );
      this.showModal = 1;

      if (this.infoChapter.finished) {
        this.timeVideo = 0;
      }
    },

    async saveTime() {
      let time = await this.player.getCurrentTime();
      const chapterStatus = {
        chapter: this.id,
        finished: false,
        timeVideo: Math.round(time),
      };
      if (!this.infoChapter.finished) {
        await this.$store.dispatch("chapterModule/updateState", chapterStatus);
      }
    },

    closeModal() {
      window.parent.postMessage("close-modal", "*");
    },

    reloadPage() {
      location.reload();
    },
  },
};
</script>

 <style scoped lang="scss"> 
.Youtube {
  width: 100%;
  height: 100%;
  background: black;
  margin: auto;
  display: flex;
  justify-content: center;
  align-items: center;

  .modal {
    width: 25%;
    padding: 3rem;
    text-align: center;

    .iconsSvg {
      width: 5rem;
      margin: 0.5rem;
      cursor: pointer;
      transition: transform 0.5s ease-in-out 0.5s;

      &:hover {
        transform: scale(1.2);
      }
    }
  }
}

.video {
  width: 100%;
  height: 100%;
}

.spinner {
  margin: auto;
  margin-top: 20%;
}
</style>