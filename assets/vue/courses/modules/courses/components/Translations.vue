<template>
  <div
    class="d-flex align-items-center justify-content-center"
    v-if="isLoading"
  >
    <Spinner />
  </div>
  <div v-else>
    <div class="table-responsive">
      <table class="table">
        <thead>
          <tr>
            <th>{{ $t("CODE") }}</th>
            <th>{{ $t("NAME") }}</th>
            <th>{{ $t("CATEGORY") }}</th>
            <th>{{ $t("LOCALE") }}</th>
            <th>{{ $t("CHAPTERS.LABEL.PLURAL") }}</th>
            <th>{{ $t("COURSE.ACTIVE") }}</th>
            <th>{{ $t("CREATED_AT") }}</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="courseTranslate in courseTranslates"
            :key="courseTranslate.id"
          >
            <td>
              <span @click="goToView(courseTranslate.id, courseTranslate.name)" style="cursor: pointer; color: hsl(198, 99%, 34%); text-decoration: underline;">
                {{ courseTranslate.code }}
              </span>
            </td>
            <td>
              <div class="container-name-course">
                <img :src="courseTranslate.thumbnailUrl" />
                {{ courseTranslate.name }}
              </div>
            </td>
            <td>{{ courseTranslate.category }}</td>
            <td style="text-transform: uppercase">{{ courseTranslate.locale }}</td>
            <td>{{ courseTranslate.totalChapters }}</td>
            <td>
              <BaseSwitch
                  class="justify-content-center"
                  :tag="`switcher-pages-${courseTranslate.id}`"
                  v-model="courseTranslate.active"
                  :disabled="!$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.TRANSLATIONS)"
                  @change="publishCourse(courseTranslate)"
                  theme="light"
              />
            </td>
            <td>{{ courseTranslate.createdAt }}</td>
            <td class="text-end">
              <router-link
                :to="{
                  name: ROUTE_NAMES.VIEW_COURSE,
                  params: {
                    id: courseTranslate.id,
                    name: courseTranslate.name,
                  },
                }"
                >
                <button class="btn btn-info btn-sm" @click="reloading">
                  <i class="fa fa-eye"></i>
                </button>
              </router-link>
              <router-link
                v-if="$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.TRANSLATIONS)"
                :to="{
                  name: ROUTE_NAMES.UPDATE_COURSE,
                  params: { id: courseTranslate.id },
                }"
                class="btn btn-primary btn-sm"
                ><i class="fa fa-pencil"></i
              ></router-link>
              <button
                v-if="$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.TRANSLATIONS)"
                class="btn btn-danger btn-sm"
                @click="deleteTranslate(courseTranslate.id)"
              >
                <i class="fa fa-trash"></i>
              </button>
            </td>
          </tr>
          <tr v-if="courseTranslates.length === 0">
            <td colspan="12">
              <BaseNotResult />
            </td>
          </tr>
        </tbody>
      </table>
      <p class="mt-4">
        <b>{{ courseTranslates.length }}</b> {{ $t("RESULTS") }}
      </p>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import Spinner from "../../../../admin/components/base/Spinner.vue";
import BaseNotResult from "../../../../base/BaseNotResult.vue";
import ROUTE_NAMES from "../router/routeNames";
import BaseSwitch from "../../../../base/BaseSwitch.vue";
import { COURSE_PERMISSIONS } from '../../../../common/utils/auth/permissions/course.permissions'

export default {
  name: "Translations",
  components: {BaseSwitch, BaseNotResult, Spinner },
  data() {
    return {
      ROUTE_NAMES,
      isLoading: true,
    };
  },
  computed: {
    COURSE_PERMISSIONS() {
      return COURSE_PERMISSIONS
    },
    courseTranslates: get("coursesModule/getCourseInfoTranslates"),
  },
  async created() {
    this.isLoading = true;
    try {
      await this.fetchTranslates();
    } catch (e) {
      console.error("fetchTranslates error:", e);
    } finally {
      this.isLoading = false;
    }
  },
  methods: {
    async fetchTranslates() {
      const courseId = this.$route.params.id;
      await this.$store.dispatch(
        "coursesModule/fetchCourseTranslateById",
        courseId
      );
    },
    reloading(){
      setTimeout(() => {
        location.reload();
      }, 300);
    },
    async deleteTranslate(id) {
      try {
        await this.$alertify.confirmWithTitle(
          this.$t("DELETE"),
          this.$t("COMMON_AREAS.QUESTION_DELETE"),
          async () => {
            const courseId = this.$route.params.id;
            await this.$store.dispatch("coursesModule/deleteCourse", id);
            await this.$store.dispatch("coursesModule/fetchCourseTranslateById", courseId);
            this.$toast.success(this.$t("DELETE_SUCCESS"));
          },
          () => {}
        );
      } catch (e) {
        this.$toast.error(this.$t("DELETE_FAILED"));
      }
    },
    async publishCourse(courseTranslation) {
      if (!this.$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.TRANSLATIONS)) return;
      let response = await this.$store.dispatch("coursesModule/updatePublishCourse", courseTranslation.id);
      if(response && response.error){
        this.$toast.error(this.$t(response.message));
        this.$nextTick(() => {
          courseTranslation.active = false;
        });
      }
      else{
        this.$toast.success(this.$t("SUCCESS"));
      }
    },
    goToView(id, name) {
      this.$router.push({ name: ROUTE_NAMES.VIEW_COURSE, params: { id, name } }).catch()
    },
  },
};
</script>

<style scoped lang="scss">
.table-responsive {
  min-height: 400px;
}
.container-name-course {
  display: flex;
  gap: 1rem;
  align-items: center;
  a {
    font-size: 1rem;
  }
  img {
    width: 50px;
    height: 30px;
    flex-shrink: 0;
  }
}
</style>
