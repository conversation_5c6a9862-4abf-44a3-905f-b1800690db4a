<template>
  <div
    class="BaseModalInspector modal fade"
    :id="identifier"
    tabindex="-1"
    aria-labelledby="baseModalInspector"
    aria-hidden="true"
  >
    <div :class="`modal-dialog modal-dialog-centered ${size}`">
      <div class="modal-content">
        <div class="modal-header bg-dark" v-if="isHeader">
          <h5 class="modal-title" id="staticBackdropLabel">{{ title }}</h5>
          <div v-if="search" class="col-4 mr-auto d-flex flex-row align-items-center">
            <i class="fas fa-search content-search-icon"></i>
            <label class="content-search-label w-100 mb-0 ml-1">
              <input type="search" class="form-control is-blank" @input="$emit('on-search', $event.target.value)" spellcheck="false">
            </label>
          </div>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
            :data-modal-id="'#' + identifier"
          ></button>
        </div>
        <div class="modal-body" :style="{ padding: padding }">
          <slot> </slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import $ from "jquery";
export default {
  name: "BaseModalInspector",
  props: {
    identifier: {
      type: String,
      required: true,
    },

    title: {
      type: String,
      default: "Modal title",
    },

    size: {
      type: String,
      default: "modal-lg",
    },

    isHeader: {
      type: Boolean,
      default: true,
    },

    padding: {
      type: String,
      default: "1rem",
    },

    search: {
      type: Boolean,
      default: false
    }
  },

  methods: {
    closeModal() {
      $(`#${this.identifier}`).modal("hide");
    },
  },
};
</script>

 <style scoped lang="scss"> 
.BaseModalInspector {
  img {
    width: 100%;
    height: 40rem;
  }

  .modal-fullscreen {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }

  .modal-xl {
    max-width: 1300px;
  }
}
</style>
