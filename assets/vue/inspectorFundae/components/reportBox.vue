<template>
<div class="ReportBox">
  <h5 class="d-flex justify-content-start align-items-center text-primary mb-4">
    <BaseSwitch :tag="tag+'_activegroup_'" v-model="activateGroup" @change="emitBox"/>
    <span class="ml-3">{{ title }}</span>
  </h5>
  <div class="content">
    <CheckItem
        :tag="tag + '_ci' + index"
        v-for="(item, index) in options"
        :key="tag + '_ci' + index"
        :title="item.title"
        :options="item.options"
        :active="true"
        :all="activateGroup"
        @toggle="(value) => emitChange(index, value)"
    />
  </div>
</div>
</template>

<script>
import CheckItem  from "./checkItem";
import BaseSwitch from "../../base/BaseSwitch.vue"

export default {
  name: "reportBox",
  components: { CheckItem, BaseSwitch},
  props: {
    tag: { type: String, default: 'ReportBox' },
    title: { type: String, default: '' },
    value: { type: Boolean, default: false },
    options: { type: Array, default: () => ([]) },
  },
  data() {
    return {
      activateGroup: false,
    }
  },
  mounted() {
    this.activateGroup = this.value
  },
  methods: {
    emitChange(index, value) {
      this.$emit('toggle', [index, ...value])
    },
    emitBox(value) {
      this.$emit('active', this.activateGroup);
    }
  }
}
</script>

 <style scoped lang="scss"> 
.ReportBox {
  background-color: var(--color-neutral-lighter);
  padding: 1rem;
  border: 1px solid var(--color-neutral-mid);
  border-radius: 6px;

  .content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
}
</style>
