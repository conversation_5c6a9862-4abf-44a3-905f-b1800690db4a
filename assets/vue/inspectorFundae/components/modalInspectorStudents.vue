<template>
<div class="modalInspectorStudents">
  <BaseModalInspector
      :identifier="tag + 'modalInspectorStudents'"
      :title="`${$t('ANNOUNCEMENT.FORM.STEPS.STUDENTS')}`"
      :search="true"
      @on-search="query = $event"
      padding="2rem"
      size="modal-xl">
    <StudentsTable :query="query" :student-list="groupSelected.users || []" :allow-actions="false" :inspector="true"/>
  </BaseModalInspector>
</div>
</template>

<script>
import BaseModalInspector from "./BaseModalInspector";
import StudentsTable      from "../../announcement/components/details/studentsTable";
import {get}              from "vuex-pathify";
export default {
  name: "modalInspectorStudents",
  components: {StudentsTable, BaseModalInspector},
  props: {
    tag: { type: String, default: '' },
  },
  data() {
    return {
      query: ''
    };
  },
  computed: {
    groupSelected: get('announcementModule/groupSelected'),
  }
}
</script>

 <style scoped lang="scss"> 
.modalInspectorStudents { }
</style>
