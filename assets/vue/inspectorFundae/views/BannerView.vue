<template>
  <div class="BannerView bg-primary-dark">
    <div class="content text-white p-3">
      <h4 class="mb-0">{{ course.name || '' }}</h4>
      <div class="dataContainer d-flex pt-3 gap-3 flex-wrap">
        <div class="data d-table">
          <ResumeTag icon="fa-dice-d20" :title="`${$t('ANNOUNCEMENT.DEFAULT.MODALITY')}:`" :answer="course.type"/>
          <ResumeTag icon="fa-calendar" :title="`${$t('ANNOUNCEMENT.SESSIONSTAB.START')}:`" :answer="getDate(announcement?.startAt)"/>
          <ResumeTag icon="fa-calendar" :title="`${$t('INSPECTORVIEW.END')}:`" :answer="getDate(announcement?.finishAt)"/>
          <ResumeTag icon="fa-clock" :title="`${$t('INSPECTORVIEW.TOTAL_HOURS')}:`" :answer="announcement?.totalHours || 0"/>
        </div>
        <div class="data">
          <ResumeTag icon="fa-file-pdf" :title="`${$t('ANNOUNCEMENT.FORM.ENTITY.DIDACTIC_GUIDE')}:`" :answer="guideText"
                     :clickable="hasGuide" @clicked="printGuide"/>
          <ResumeTag icon="fa-user-tag" :title="`${$t('ANNOUNCEMENT.FORM.ENTITY.CONTACT_PERSON')}:`"  :answer="bonification.contactPerson"/>
          <ResumeTag icon="fa-envelope" :title="`${$t('ANNOUNCEMENT.FORM.ENTITY.CONTACT_PERSON_EMAIL')}:`" :answer="bonification.contactPersonEmail"
                     :clickable="hasValidEmail" @clicked="openMail"/>
          <ResumeTag icon="fa-mobile" :title="`${$t('ANNOUNCEMENT.FORM.ENTITY.CONTACT_PERSON_TELEPHONE')}:`" :answer="bonification.contactPersonTelephone"/>
        </div>
        <div class="data" style="max-height: 70px">
          <ResumeTag icon="fa-barcode" :title="`${$t('ANNOUNCEMENT.FORM.ENTITY.ACTION_CODE')}:`" :answer="bonification.codeAction"/>
          <ResumeTag icon="fa-users" :title="`${$t('ANNOUNCEMENT.FORM.ENTITY.MAX_GROUP_SIZE')}:`" :answer="bonification.maxByGroup"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ResumeTag    from "../components/resumeTag";
import {get}        from "vuex-pathify";
import {UtilsMixin} from "../../announcement/mixins/utilsMixin";
export default {
  name: "BannerView",
  mixins: [UtilsMixin],
  components: {ResumeTag},
  computed: {
    announcement: get('announcementModule/announcement'),
    course() {
      return (this.announcement?.course || {})
    },
    bonification() {
      return (this.announcement?.bonification || {})
    },
    hasGuide() {
      return !!(this.announcement?.guideURL)
    },
    guideText() {
      return this.hasGuide ? 'Ver/Imprimir' : '--';
    },
    hasValidEmail() {
      return !!(String(this.bonification.contactPersonEmail || '').toLowerCase().match(
          /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      ))
    }
  },
  methods: {
    printGuide() {
      const link = document.createElement("a");
      link.href = this.announcement?.guideURL || '#';
      link.download = this.announcement?.guideTitle || '';
      link.click();
    },
    openMail() {
      const link = document.createElement("a");
      link.href = `mailto:${this.bonification.contactPersonEmail}`;
      link.click();
    }
  }
}
</script>

 <style scoped lang="scss"> 
.BannerView {
  .dataContainer {
    gap: 1rem;

    .data {
      border-radius: 7px;
      padding: 0.5rem 1rem;
      background-color: var(--color-neutral-lightest);
      color: var(--color-neutral-darker);
    }
  }
}
</style>
