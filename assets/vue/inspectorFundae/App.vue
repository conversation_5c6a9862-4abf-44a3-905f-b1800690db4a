<script>
import {get} from "vuex-pathify";
import Spinner from "../admin/components/base/Spinner.vue";
import HeaderView from "./views/HeaderView.vue";

export default {
  name: "App",
  components: {HeaderView, Spinner},
  computed: {
    loading: get('loaderModule/loading'),
    message: get('loaderModule/message'),
    isAuthenticated: get('authModule/isAuthenticated')
  },
}
</script>

<template>
  <div class="InspectorApp">
    <router-view />
  </div>
</template>

<style scoped lang="scss">
.InspectorApp {
  display: flex;
  flex-flow: column;
  min-height: 100vh;
  min-width: 350px;
  width: 100vw;
  overflow: hidden;
  background: linear-gradient(90deg, #94BDBF, #CFF9FF);

  ::v-deep {
    .bg-primary-dark {
      background-color: #004D6D !important;
    }

    .bg-dark {
      background-color: var(--color-neutral-darker);
      color: white;
    }

    .content {
      width: clamp(200px, 100%, 1400px);
      margin: auto;
    }

    .cursor-pointer {
      cursor: pointer;
    }

    .line-break-anywhere {
      overflow-wrap: break-word;
    }

    ::v-deep {
      .line-break-anywhere {
        overflow-wrap: break-word;
      }
    }
  }
}
</style>
