import {dispatch, make} from "vuex-pathify";
import axios from "axios";
import TaskQueueService from '../../../common/services/TaskQueueService';

const state = {
    loading: false,
    itineraries: [],
    courses: [],
    tags: [],
    translations: [],
    filters: {
        courseId: null,
        tags: [],
        active: "",
    },
    pagination: {
        page: 1,
        totalItems: 0
    },
    query: ''
};
const mutations = {
    ...make.mutations(state),
};

export const getters = {
    ...make.getters(state) ,
    getTranslations: (state) => () => state.translations,
};

export const actions = {
    ...make.actions(state),
    async getItineraries({ commit, getters}) { 
        commit('SET_LOADING', true);
        try {
            const { page } = getters['pagination'];
            const { courseId, tags, active } = getters['filters'];
            const query = getters['query'];
            const url = new URL(window.location.origin + `/admin/itinerary/itineraries/${page}`);
            
            if (query && query.length > 0) {
                url.searchParams.append('query', query);
            }
            
            if (courseId && courseId > 0) url.searchParams.append('course', courseId);
            if (tags && tags.length) url.searchParams.append('tags', JSON.stringify(tags.map((tag) => tag.id)));
            if (active && active !== "") url.searchParams.append('status', active);
            
            const result = await axios.get(url.toString());
            const { data } = result.data;

            commit('SET_ITINERARIES', data.items);
            commit('SET_PAGINATION', {
                page,
                totalItems: data['total-items']
            });

            return result.data;
        } finally {
            commit('SET_LOADING', false);
        }
    },

    async deleteItinerary({ commit, dispatch }, id) {
        const result = await axios.delete(`/admin/itinerary/${id}`);
        const { error } = result.data;
        if (!error) dispatch('getItineraries')
        return result.data;
    },

    saveItinerary({ }, payload) {
        return axios.post(`/admin/itinerary/save`, payload).then((response) => response.data);
    },

    async getAvailableCourses({ commit }) {
        const result = await axios.get('/admin/courses/available');
        const { data } = result.data;
        commit('SET_COURSES', data);
    },

    async getAvailableTags({ commit }) {
        const result = await axios.get('/admin/itinerary/tags');
        const { data } = result.data;
        commit('SET_TAGS', data);
    },

    async getItineraryTranslations({ commit }, { endpoint}) {
        try {
            commit("SET_LOADING", true);
      
            const result = await axios.get(endpoint);
            const { data } = result.data;
            commit('SET_TRANSLATIONS', data);;
            
          } catch (error) {
            console.log(error);
          } finally {
            commit("SET_LOADING", false);
          }        
    },

    async getUrl({ commit }, {id, action = 'detail'}) {
        const url = new URL(window.location.origin + `/admin/itinerary/${id}/generate-url`);
        url.searchParams.append('action', action);
        const result = await axios.get(url.toString());
        const { data } = result.data;
        window.location.href = data;
    },

    async activateItinerary({}, { id, active }) {
        const result = await axios.patch(`/admin/itinerary/${id}/activate`, { active });
        return result.data;
    },

    async changeSort({ commit }, { endpoint, requestData}) {
      try {
        const { data, error } = await axios.put(endpoint, requestData);
  
        if (error) {
          throw new Error(data?.message);
        }
      } catch (error) {
        console.log(error);
      }
    },

    async cloneItinerary({ commit }, { endpoint, requestData}) {
      try {
        const { data, error } = await axios.post(endpoint, requestData);
  
        if (error) {
          throw new Error(data?.message);
        }
      } catch (error) {
        console.log(error);
      }
    },

    async handleSearch({ commit, dispatch }, query) {
        commit('SET_QUERY', query);
        commit('SET_PAGINATION', {
            ...state.pagination,
            page: 1
        });
        await dispatch('getItineraries');
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions
}
