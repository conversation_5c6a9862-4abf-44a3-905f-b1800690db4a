<script>
import { get, sync } from "vuex-pathify";
import Spinner from "../../../admin/components/base/Spinner.vue";
import Pagination from "../../../admin/components/Pagination.vue";
import RadioButton from "../../../common/components/RadioButton.vue";
import Multiselect from "vue-multiselect";

export default {
  name: "AnnouncementCourse",
  components: { RadioButton, Pagination, Spinner, Multiselect },
  props: {
      isZipReport: { type: Boolean, default: false },
      loadCourseFilters: {
        type: Function,
        required: false,
    },
  },
  data() {
    return {
      loading: false,
      load: false,
      query: "",
      courses: [],
      totalCourses: 0,
      // pagedCourses: [],
      currentPage: 1,
      pageSize: 10,
      optionsPageSize: [10, 15, 20, 25],
      coursesStatus: this.isZipReport ? 2 : null,
      onlyActivesList: [
		    { id: 0, name: 'Inactivos' },				
		    { id: 1, name: 'Activos' },
		    { id: 2, name: 'Todos' },
	    ],
    };
  },
  computed: {
    enableCourseFinder: get("announcementFormModule/enableCourseFinder"),
    update: get("announcementFormModule/update"),
    course: sync("announcementFormModule/announcement@course"),
    defaultImage: get("configModule/defaultImageB64"),
    type: sync("announcementFormModule/announcement@type"),
    types: get("announcementFormModule/types"),

    idTypeCourse() {
      return this.types.find((type) => type.type === this.type)?.id;
    },
    edit() {
      return this.enableCourseFinder && !this.update;
    },
  },

  watch: {
    /* type() {
      this.currentPage = 1;
      if (this.load || this.types.length === 1) this.findCourses();
    },*/
    course: {
      deep: true,
      immediate: true,
      handler: function () {
        if (!this.enableCourseFinder) {
          this.preSelectedCourse();
        }
      },
    },
    currentPage() {
      this.findCourses();
    },
    pageSize() {
      if (this.courses.length > 0 && this.totalCourses < this.pageSize) {
        this.currentPage = 1;
      }
      this.findCourses();
    },
  },
  mounted() {
    if (this.load || this.types.length === 1 || this.isZipReport) this.findCourses();
    if (this.course && this.course.id) {
      this.type = this.course.type;
    }
  },

  methods: {
    preSelectedCourse() {
      const temp = [];
      temp.push(this.course);

      this.courses = temp;
    },
    findCourses() {
      if (!this.enableCourseFinder) {
        this.preSelectedCourse();

        return;
      } // If is an update, avoid calling courses

      this.loading = true;
      this.$store
        .dispatch("announcementFormModule/findCourses", {
          query: this.query,
          typeCourse: !this.isZipReport ? this.idTypeCourse : 1,
          page: this.currentPage,
          pageSize: this.pageSize,
          coursesStatus: this.coursesStatus
        })
        .then((res) => {
          const { error, data } = res;
          if (error) this.$toast.error("Failed to load data");
          else {
            const { items, totalItems } = data;
            this.courses = items;
            this.totalCourses = totalItems;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    changeState(type) {
      this.type = type;
      this.load = true;
      this.currentPage = 1;
      if (this.load || this.types.length === 1) this.findCourses();
    },
    setCourseId(course){
      this.course = course;
      if(this.isZipReport){
        this.$emit('update-value', course.id);
        this.loadCourseFilters();
      }
    }
    // onCurrentPage(items) {
    //   this.pagedCourses = items;
    // },
  },
};
</script>

<template>
  <div class="AnnouncementCourse">
    <div class="col-12 p-0 AnnouncementCourse--searchbar">
      <input
        type="text"
        class="form-control"
        :class="{ isZipReport: isZipReport }"
        :placeholder="$t('ANNOUNCEMENT.FIND_COURSE')"
        v-model="query"
        @keydown.enter="
          currentPage = 1;
          findCourses();
        "
        :disabled="!edit || loading"
      />
      <div v-if="isZipReport">
          <div class="form-group text-nowrap">
                <label for="courseStatus" class="text-left">{{ $t('FILTER.USER.COURSE.STATE') }}</label> 
              <select @change="findCourses()" id="courseStatus" v-model="coursesStatus" :list="onlyActivesList" :empty="'Estados de cursos'" name="courseStatus" class="form-select" >
                  <option :value=0>{{ $t('USERS.INACTIVE') }}</option>
                  <option :value=1 >{{ $t('USERS.ACTIVE') }}</option>
                  <option :value=2>{{ $t('ALL') }}</option>
              </select>
          </div>
      </div>

      <div v-else>
        <div
          class="AnnouncementCourse--searchbar--actions"
          v-if="!enableCourseFinder"
        >
          <radio-button
            v-for="radio in types"
            :label="radio.name"
            :id="'action_'+radio.type"
            :icon="radio.icon"
            v-model="type"
            :radio-value="radio.type"
            name="type_selection"
            :disabled="!edit || loading"
            :key="radio?.id"
          />
        </div>
  
        <div class="AnnouncementCourse--searchbar--actions" v-else>
          <radio-button
            v-for="radio in types"
            :label="radio.name"
            :id="'action_' + radio.type"
            :icon="radio.icon"
            :radio-value="radio.type"
            name="type_selection"
            :disabled="!edit || loading"
            :key="radio?.id"
            @input="changeState(radio.type)"
          />
        </div>
      </div>
    </div>
    <div
      class="d-flex flex-column align-items-center justify-content-center"
      v-if="loading"
    >
      <spinner />
    </div>
    <div class="col-12 AnnouncementCourse--courses" v-else>
      <div
        class="AnnouncementCourse--courses--course"
        :class="course?.id === item?.id ? 'selected' : ''"
        v-for="item in courses"
        :key="item?.id"
        @click="setCourseId(item)"
      >
        <img
          :src="item?.thumbnailUrl ? item.thumbnailUrl : defaultImage"
          alt=""
          @error="$event.target.src = defaultImage"
        />
        <p>
          <span>{{ item?.code }} - {{ item?.name }}</span>
        </p>
      </div>
    </div>
    <div class="col-12 d-grid" v-if="edit">
      <pagination
        class="align-self-center"
        :page-size="pageSize"
        :total-items="totalCourses"
        :prop-current-page="currentPage"
        @current-page="currentPage = $event"
        v-show="totalCourses > pageSize"
      />
      <div style="width: 65px; justify-self: center" v-if="totalCourses > 0">
        <Multiselect
          :options="optionsPageSize"
          v-model="pageSize"
          :show-labels="false"
          :allow-empty="false"
          :placeholder="$t('MULTISELECT.PLACEHOLDER')"
          :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
        />
      </div>
    </div>

    <div class="col-12" v-if="!loading && courses.length === 0">
      <BaseNotResult />
    </div>
  </div>
</template>

<style scoped lang="scss">
.isZipReport{
  margin-top: 40px;
}
.AnnouncementCourse {
  width: 100%;
  background-color: #ffffff;
  padding: 1rem 0.25rem;

  &--searchbar {
    display: flex;
    flex-flow: column;
    gap: 1rem;
    z-index: 2;

    @media #{min-small-screen()} {
      display: grid;
      grid-template-columns: 30% 1fr;
    }

    @media #{min-medium-screen()} {
      gap: 5rem;
    }

    @media #{min-large-screen()} {
      gap: 10rem;
    }

    & > input {
      width: 100%;
    }

    &--actions {
      width: 100%;
      align-self: flex-end;
      display: grid;
      gap: 0.5rem;

      @media screen and (min-width: 426px) {
        grid-template-columns: repeat(2, auto);
      }

      @media #{min-small-screen()} {
        grid-template-columns: repeat(4, auto);
      }

      .RadioButton {
        width: 100%;
      }
    }
  }

  &--courses {
    width: 100%;
    display: flex;
    flex-flow: row wrap;
    row-gap: 1rem;
    padding: 1rem 0;
    overflow-y: auto;

    @media #{small-screen()} {
      display: grid;
      grid-template-columns: repeat(auto-fit, calc((100% - 1rem) / 2));
      gap: 1rem;
    }

    @media #{min-medium-screen()} {
      display: grid;
      grid-template-columns: repeat(auto-fit, calc((100% - 3rem) / 4));
      gap: 1rem;
    }

    &--course {
      position: relative;
      background-size: cover;
      background-position: center center;
      cursor: pointer;
      display: flex;
      color: #ffffff;
      width: 100%;
      height: 150px;

      img {
        position: absolute;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
        object-fit: cover;
        object-position: center;
      }

      p {
        z-index: 5;
        font-size: 17px;
        color: #ffffff;
        width: 100%;
        overflow: hidden;
        text-shadow: 1px 1px 3px #000000;
        background: linear-gradient(
          0deg,
          rgba(2, 0, 36, 0.3) 0%,
          rgba(129, 129, 129, 0.3) 32%,
          rgba(129, 129, 129, 0.3) 69%,
          rgba(4, 4, 4, 0.3) 100%
        );
        height: 100%;
        display: flex;
        padding: 0.3rem;

        span {
          margin-top: auto;
          width: 100%;
          text-align: left;
        }
      }
      transition: all 0.5s ease-in;

      &:not(.selected):hover {
        transform: scale(1.05);
      }

      &:after {
        content: "";
        height: 2px;
        width: 0;
        position: absolute;
        bottom: 0;
        left: 0;
        background-color: $base-border-color-active;
      }

      &.selected {
        border: 5px solid var(--color-primary);
      }

      span {
        text-align: center;
      }
    }
  }
}
</style>
