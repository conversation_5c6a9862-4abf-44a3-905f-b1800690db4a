<script>
import {get, sync} from "vuex-pathify";
import {configurationClientAnnouncement} from "../../mixins/configurationClientAnnouncement";

import ButtonWithDescription from "../../../common/components/ButtonWithDescription.vue";
import RadioButton from "../../../common/components/RadioButton.vue";
import LabelWithInfo from "../../../common/components/ui/LabelWithInfo.vue";
import AnnouncementConfigurationType from "../../mixins/AnnouncementConfigurationType";

export default {
  name: "AnnouncementBonus",
  components: {LabelWithInfo, RadioButton, ButtonWithDescription},
  mixins: [configurationClientAnnouncement],
  data() {
    return {
      accessVisible: false
    };
  },
  computed: {
    type: get("announcementFormModule/announcement@type"),
    stepsConfigurations: get("announcementFormModule/stepsConfigurations"),// All steps configurations
    stepInfo() {
      return this.stepsConfigurations[this.type].steps.find(s => s.type === 'AnnouncementBonus') ?? {};
    },
    announcementConfigurationTypes() {
      return this.stepInfo?.configurations ?? [];
    },

    availableCodes: get('announcementFormModule/codes'),
    isNotified: get("announcementFormModule/isNotified"),
    subsidizedProperty: sync("announcementFormModule/announcement@subsidized"),
    actionType: sync("announcementFormModule/announcement@actionType"),
    actionCode: sync("announcementFormModule/announcement@actionCode"),
    denomination: sync("announcementFormModule/announcement@denomination"),
    configAnnouncement: sync(
        "announcementFormModule/announcement@configAnnouncement"
    ),


    approvedCriteriaValues: sync(
        "announcementFormModule/announcement@approvedCriteriaValues"
    ),

    usersPerGroup: sync("announcementFormModule/announcement@usersPerGroup"),

    contactPerson: sync("announcementFormModule/announcement@contactPerson"),
    contactPersonEmail: sync(
        "announcementFormModule/announcement@contactPersonEmail"
    ),
    contactPersonTelephone: sync(
        "announcementFormModule/announcement@contactPersonTelephone"
    ),

    inspectorAccess: sync(
        "announcementFormModule/announcement@inspectorAccess"
    ),
    types: get("announcementFormModule/types"),
    actionTypes: get("announcementFormModule/fundaeConfiguration@actionTypes"),
    maxStudentsPerGroup: get("announcementFormModule/fundaeConfiguration@maxStudentsPerGroup"),
    minPassingScore: get("announcementFormModule/fundaeConfiguration@minPassingScore"),

    selectedTypeInfo() {
      const index = this.types.findIndex((t) => t.type === this.type);
      return this.types[index];
    },

    // configurationsClientInThisPage() {
    //   const configurations = [
    //     {
    //       type: "BONIFICATION"
    //     },
    //   ];
    //
    //   return this.fetchConfigurationByComponent(configurations);
    // },
    subsidized() {
      const conf = this.announcementConfigurationTypes.find(c => c.id === AnnouncementConfigurationType.SUBSIDIZED);
      if (conf === undefined) return false;
      return this.configAnnouncement[AnnouncementConfigurationType.getVModelFromId(AnnouncementConfigurationType.SUBSIDIZED)] ?? false;
    }
  },
  watch: {
    subsidized: {
      immediate: true,
      handler: function (newVal) {
        this.subsidizedProperty = newVal;
        if (newVal) {
          if (this.usersPerGroup > this.maxStudentsPerGroup) this.usersPerGroup = this.maxStudentsPerGroup;

          if (this.type === 'online' || this.type === 'mixed') {
            const keys = Object.keys(this.approvedCriteriaValues);
            const approvedValues = structuredClone(this.approvedCriteriaValues);
            keys.forEach(key => {
              if (`${key}` === `1` && approvedValues[key].enabled && approvedValues[key].value < this.minPassingScore && approvedValues[key].dataType !== 'MINUTES')
                approvedValues[key].value = this.minPassingScore;
            });
            this.approvedCriteriaValues = approvedValues;
          }

          if (this.actionCode === undefined || this.actionCode.length < 1)
            this.actionCode = this.availableCodes.actionCode;
        } else {
          this.actionType = null;
          this.actionCode = '';
          this.denomination = '';
          this.contactPerson = '';
          this.contactPersonEmail = '';
          this.contactPersonTelephone = '';
        }
      }
    },
    usersPerGroup() {
      if (this.subsidized && this.usersPerGroup > this.maxStudentsPerGroup) this.usersPerGroup = this.maxStudentsPerGroup;
    },
    contactPersonTelephone() {
      let isNum = /^\d+$/.test(this.contactPersonTelephone);
      if (!isNum && this.contactPersonTelephone.length > 0) this.contactPersonTelephone = this.contactPersonTelephone.replace(/\D/g, '');
    }
  },
  methods: {
    copy(value) {
      navigator.clipboard.writeText(value).then(() => {
        this.$toast.success('Copiado');
      }).catch(() => {
        this.$toast.error('Failed to copy');
      });
    }
  }
};
</script>

<template>
  <div class="AnnouncementBonus">
    <button type="button" class="AnnouncementBonus--access-btn btn btn-primary" @click="accessVisible = !accessVisible">Acceso</button>
    <div class="AnnouncementBonus--bonus">
      <button-with-description
          v-for="config in announcementConfigurationTypes"
          :key="config.id"
          :name="config.vModelName"
          :title="config.name"
          :description="config.description"
          :image-url="config.image"
          v-model="configAnnouncement[`configuration-${config.id}`]"
          :disabled="isNotified"
      />

      <div class="AnnouncementBonus--bonus--info">
        <div>
          <div class="form-group info-icon" id="AnnouncementBonus-actionType" :class="subsidized ? 'required' : ''">
            <label>
              {{ $t('ANNOUNCEMENT.FORM.ENTITY.BONIFICATION_TYPE') }}
            </label>
            <select class="form-select"
                    v-model="actionType"
                    :required="subsidized"
                    :disabled="!subsidized || isNotified">
              <option v-for="fActionType in actionTypes" :value="fActionType.value">
                {{ $t(fActionType.name) }}
              </option>
            </select>
          </div>
          <div class="form-group info-icon" id="AnnouncementBonus-actionCode" :class="subsidized ? 'required' : ''">
            <label-with-info :info="$t('ANNOUNCEMENT.FORM.ENTITY.ACTION_CODE_INFO') + ''">
              {{ $t('ANNOUNCEMENT.FORM.ENTITY.ACTION_CODE') }}
            </label-with-info>
            <input type="text"
                   class="form-control"
                   v-model="actionCode"
                   :required="subsidized"
                   :disabled="!subsidized || isNotified"/>
          </div>
          <div>
            <label>{{ $t('ANNOUNCEMENT.DEFAULT.MODALITY') }}</label>
            <radio-button
                id="bonus-modality"
                name="bonus-modality"
                class="no-width"
                :label="selectedTypeInfo.name"
                :id="selectedTypeInfo.type"
                :icon="selectedTypeInfo.icon"
                :radio-value="selectedTypeInfo.name"
                :disabled="true"
                v-model="type"
            />
          </div>
         <!--  <div class="form-group" id="AnnouncementBonus-denomination">
          <label>{{ $t('ANNOUNCEMENT.FORM.ENTITY.DENOMINATION') }}</label>
            <input type="text"
                   class="form-control"
                   v-model="denomination"
                   :disabled="!subsidized || isNotified"/>
          </div> -->

          <div class="form-group info-icon" id="AnnouncementBonus-usersPerGroup" :class="subsidized ? 'required' : ''">
            <label-with-info :info="$t('ANNOUNCEMENT.FORM.ENTITY.MAX_GROUP_SIZE_INFO') + ''">
              {{ $t('ANNOUNCEMENT.FORM.ENTITY.MAX_GROUP_SIZE') }}
            </label-with-info>
            <input
                type="number"
                min="0"
                :max="maxStudentsPerGroup"
                step="1"
                class="form-control"
                v-model.number="usersPerGroup"
                :disabled="!subsidized || isNotified"
                :required="subsidized"
            />
          </div>
        </div>

        <div class="AnnouncementBonus--bonus--info--contact">
          <div class="avatar">
            <i class="fa fa-user"></i>
          </div>
          <div class="form-group col-12" id="AnnouncementBonus-contactPerson" :class="subsidized ? 'required' : ''">
            <label>{{ $t('ANNOUNCEMENT.FORM.ENTITY.CONTACT_PERSON') }}</label>
            <input type="text"
                   class="form-control"
                   v-model="contactPerson"
                   :required="subsidized"
                   :disabled="!subsidized || isNotified"/>
          </div>
          <div class="form-group col-12" id="AnnouncementBonus-contactPersonEmail" :class="subsidized ? 'required' : ''">
            <label>{{ $t('ANNOUNCEMENT.FORM.ENTITY.CONTACT_PERSON_EMAIL') }}</label>
            <input type="email"
                   class="form-control"
                   v-model="contactPersonEmail"
                   :disabled="!subsidized || isNotified"
                   :required="subsidized"
            />
          </div>
          <div class="form-group col-12" id="AnnouncementBonus-contactPersonTelephone" :class="subsidized ? 'required' : ''">
            <label>{{ $t('ANNOUNCEMENT.FORM.ENTITY.CONTACT_PERSON_TELEPHONE') }}</label>
            <input type="text"
                   class="form-control"
                   v-model="contactPersonTelephone"
                   :disabled="!subsidized || isNotified"
                   :required="subsidized"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="AnnouncementBonus--access" :class="accessVisible ? 'visible' : ''">
      <label-with-info class="AnnouncementBonus--access--info" :info="$t('ANNOUNCEMENT.FORM.ACCESS.HELP') + ''">
      </label-with-info>
      <div class="search">
        <i class="fa fa-search"></i>
      </div>
      <h1>{{ $t('ANNOUNCEMENT.FORM.ACCESS.INFO') }}</h1>
      <div class="form-group col-12 d-flex flex-row flex-nowrap">
        <div class="flex-grow-1">
          <label>{{ $t('ANNOUNCEMENT.FORM.ACCESS.URL') }}</label>
          <input type="text" class="form-control" v-model="inspectorAccess.url" readonly/>
        </div>
        <i class="far fa-copy" @click="copy(inspectorAccess.url)"></i>
      </div>
      <div class="form-group col-12 d-flex flex-row flex-nowrap">
        <div class="flex-grow-1">
          <label>{{ $t('ANNOUNCEMENT.FORM.ACCESS.USER') }}</label>
          <input
              type="text"
              class="form-control"
              v-model="inspectorAccess.user"
              readonly
          />
        </div>
        <i class="far fa-copy" @click="copy(inspectorAccess.user)"></i>
      </div>
      <div class="form-group col-12 d-flex flex-row flex-nowrap">
        <div class="flex-grow-1">
          <label>
            {{ $t("ANNOUNCEMENT.FORM.ACCESS.PASSWORD") }}
          </label>
          <input
              type="password"
              class="form-control"
              v-model="inspectorAccess.password"
              readonly
          />
        </div>
        <i class="far fa-copy" @click="copy(inspectorAccess.password)"></i>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.AnnouncementBonus {
  display: grid;
  gap: 1rem;
  position: relative;
  align-items: flex-start;

  @media #{small-screen()} {
    grid-template-columns: repeat(2, 1fr);
  }

  @media #{min-medium-screen()} {
    grid-template-columns: 1fr 350px;
  }

  &--bonus {
    display: flex;
    flex-flow: column;
    order: 2;
    @media #{min-small-screen()} {
      order: unset;
    }

    &--info {
      display: flex;
      flex-flow: column;
      gap: 1rem;

      @media #{min-medium-screen()} {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
      }

      &--contact {
        border: 1px solid #d9f0fa;
        border-radius: 5px;
        padding: 1rem 2rem;
      }
    }
  }

  .avatar,
  .search {
    @include avatar;

    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    color: #ffffff;
    width: 60px;
    height: 60px;
  }

  .search {
    background-color: var(--color-primary) !important;
  }

  &--access-btn {
    display: none;
    richness: 0;
    z-index: 10;
    position: sticky;
    right: 1rem;
    top: 1rem;
    width: 100px;
    margin-left: auto;
    @media #{extra-small-screen()} {
      display: block;
    }
  }

  &--access {
    width: 100%;
    display: none;
    flex-flow: row wrap;
    align-items: flex-start;
    justify-content: center;
    background-color: #d9f0fa;
    border-radius: 5px;
    padding: 1rem 2rem;
    position: sticky;
    top: 4rem;
    z-index: 9;
    order: 1;

    &.visible {
      display: flex;
    }

    @media #{min-small-screen()} {
      display: flex;
      position: relative;
      top: unset;
      order: unset;
    }


    h1 {
      width: 100%;
      color: var(--color-primary);
      font-size: 20px;
      text-align: center;
    }

    &--info {
      font-size: 22px;
      color: var(--color-primary);
      position: absolute;
      top: 10px;
      left: 10px;
    }

    .form-group {
      padding: 0;
    }

    .fa-copy {
      cursor: pointer;
      color: var(--color-primary);
      align-self: center;
      margin-left: .5rem;
      font-size: 20px;
    }
  }
}
</style>
