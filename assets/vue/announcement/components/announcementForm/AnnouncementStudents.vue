<script>
import { get, sync } from "vuex-pathify";
import UserFilter from "../../../common/components/filter/UserFilter.vue";
import * as XLSX from "xlsx";

import Spinner from "../../../admin/components/base/Spinner.vue";

export default {
  name: "AnnouncementStudents",
  components: { Spinner,  UserFilter },
  data() {
    return {
      usersExcel: [],
      countInvalidExcel: 0,
      uploadingFiles: false,
    };
  },
  computed: {
    announcement: get("announcementFormModule/announcement"),
    usersPerGroup: get("announcementFormModule/announcement@usersPerGroup"),
    students: sync("announcementFormModule/announcement@students"),
    confirmation: sync("announcementFormModule/confirmation"),
    isNotified: get("announcementFormModule/isNotified"),
    enrollmentTemplateXls: get("announcementFormModule/enrollmentTemplateXls"),
    numberOfStudents() {
      let total = 0;
      this.students.forEach((g) => (total += g.data.length));
      return total;
    },

    course: get("announcementFormModule/announcement@course"),
    startAt: get("announcementFormModule/announcement@startAt"),
    finishAt: get("announcementFormModule/announcement@finishAt"),

    urlAvailableUsers() {
      let announcementId = this.announcement.id ? this.announcement.id : 0;
      const url = new URL(
          window.location.origin + "/admin/announcement/form/available-students"
      );
      url.searchParams.set("announcement", announcementId);

      return url.toString();
    },
  },

  watch: {
    confirmation: {
      deep: true,
      handler: function () {
        if (
          this.confirmation.name === "AnnouncementStudents" &&
          !this.confirmation.confirmed
        ) {
          this.openConfirmationModal();
        }
      },
    },

  },

  methods: {
    openFileInput() {
      this.$refs.fileInput.click();
    },

    async handleFileUpload(event) {
      await this.$store.dispatch('announcementFormModule/setErrors', {
        'AnnouncementStudents': {
          error: false,
        }
      });

      const file = event.target.files[0];
      if (!file) return;

      try {
        const fileData = await this.readFileAsync(file);
        let rows = this.parseExcelData(fileData);
        if (rows.length <= 1) {
          this.$toast.error(this.$t('NO_DATA') + '');
          return;
        }

        rows.splice(0, 1);// Remove first row

        const duplicates = this.findDuplicateEmails(rows);
        if (duplicates.length > 0) {
          this.$toast.error(this.$t('ANNOUNCEMENT.FORM.UPLOAD_USERS.EMAIL_DUPLICATED') + '');
          let error = {
            error: true,
            type: 'AnnouncementStudents',
            data: {
              i18n: ['ANNOUNCEMENT.FORM.UPLOAD_USERS.EMAIL_DUPLICATED'],
              errorExtra: duplicates
            },
          };

          let errors = {
            'AnnouncementStudents': error
          };

          this.$store.dispatch('announcementFormModule/setErrors', errors);
          return;
        }

        if (!this.processExcelRows(rows)) {
          // Invalid email
          this.$toast.error(
            this.$t("ANNOUNCEMENT.FORM.ENTITY.USER_INVALID_EMAIL") + ""
          );
          return;
        }
        const formData = new FormData();
        formData.append("file", file);
        this.uploadingFiles = true;
        let result = await this.$store.dispatch(
          "announcementFormModule/uploadUsersExcelFile",
          formData
        );
        let error = result.error;
        let { data } = result.data;
        let not_found = result.data.not_found ?? [];

        if (!error) {
          this.$toast.success(
            this.$t("ANNOUNCEMENT.FORM.USERS.UPLOAD_FILE_SUCCESS") + ""
          );
          this.$eventBus.$emit("USER_FILTER.EVENT.SEARCH");

          if (not_found && not_found.length > 0) {
            let error = {
              error: true,
              type: 'AnnouncementStudents',
              data: {
                i18n: ['ANNOUNCEMENT.FORM.UPLOAD_USERS.USERS_NOT_FOUND'],
                errorExtra: not_found
              },
            };
            let errors = {
              'AnnouncementStudents': error
            };

            await this.$store.dispatch('announcementFormModule/setErrors', errors);
          }

          // Insert users to groups
          const students = structuredClone(this.students);
          if (students.length > 0) {
            // filter data to avoid duplication
            students.forEach((groupInfo) => {
              data = data.filter(
                (d) => groupInfo.data.find((s) => s.id === d.id) === undefined
              );
            });

            const lastGroupIndex = students.length - 1;
            if (students[lastGroupIndex].data.length < this.usersPerGroup) {
              // Insert the users in this group
              const numberOfStudents =
                this.usersPerGroup - students[lastGroupIndex].data.length;
              let toAdd =
                numberOfStudents > data.length
                  ? data
                  : data.splice(0, numberOfStudents);
              toAdd.forEach((item) => {
                students[lastGroupIndex].data.push(item);
              });
            }
          }

          // Add available users to another group
          while (data.length > 0) {
            const size =
              data.length > this.usersPerGroup
                ? this.usersPerGroup
                : data.length;
            students.push({
              id: "local_" + (students.length + 1),
              data: data.splice(0, size),
            });
          }

          const uniqueData = this.removeDuplicateGroups(students);
          this.students = uniqueData;
        }
      } catch (error) {
        this.$toast.error("Error al procesar el archivo Excel");
      } finally {
        this.uploadingFiles = false;
      }
    },

    removeDuplicateGroups(data) {
      const uniqueGroups = {};
      const users = [];

      data.forEach((item) => {
        const key = JSON.stringify(item.data);

        item.data.forEach((user) => {
          if (users.find((u) => u.email === user.email)) {
            users.find((u) => u.email === user.email).count++;
            item.data = item.data.filter((u) => u.email !== user.email);
          } else {
            users.push({ email: user.email, count: 1 });
          }
        });

        if (!uniqueGroups[key] && item.data.length > 0) {
          uniqueGroups[key] = item;
        }
      });

      const uniqueData = Object.values(uniqueGroups);

      return uniqueData;
    },

    async readFileAsync(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(e);
        reader.readAsBinaryString(file);
      });
    },

    parseExcelData(data) {
      const workbook = XLSX.read(data, { type: "binary" });
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];
      return XLSX.utils.sheet_to_json(sheet, {
        header: 1,
        blankrows: false,
      });
    },

    findDuplicateEmails(rows) {
      if (rows.length < 2) return [];
      let duplicates = [];
      let checked = [];
      rows.forEach(row => {
        if (checked.includes(row[2])) {
            duplicates.push(row[2]);
        }
        checked.push(row[2]);
      });

      return duplicates;
    },
    processExcelRows(rows) {
      this.usersExcel = [];
      this.countInvalidExcel = 0;
      return rows.slice(1).every((row) => {
        if (row.length >= 3) {
          const validRegex =
            /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
          return row[2]?.match(validRegex);
          // if (!row[3]?.match(validRegex)) {
          //   isValid = false;
          // }
          // if (row[3]?.match(validRegex)) {
          //   this.usersExcel.push({
          //     dni: row[0],
          //     firstName: row[1],
          //     lastName: row[2],
          //     email: row[3],
          //   });
          // } else {
          //   this.countInvalidExcel++;
          // }
        }
      });

      // this.messagesProcessExcel();
    },

    messagesProcessExcel() {
      if (this.countInvalidExcel > 0) {
        this.$toast.error(
          `Se encontraron ${this.countInvalidExcel} correos inválidos`
        );
      } else if (this.usersExcel.length > 0) {
        this.$toast.success(
          `Se encontraron ${this.usersExcel.length} correos válidos`
        );
      } else {
        this.$toast.error("No se encontraron correos válidos");
      }
    },

    openConfirmationModal() {
      document
        .getElementById("open--AnnouncementStudents--modal--confirmation")
        .click();
    },

    closeConfirmationModal() {
      document
        .getElementById("AnnouncementStudents--modal--confirmation_close")
        .click();
    },

    confirm() {
      this.closeConfirmationModal();
      this.confirmation.confirmed = true;
      this.$store.dispatch("announcementFormModule/nextStep");
    },
  },
};
</script>

<template>
  <div class="AnnouncementStudents">
    <div class="AnnouncementStudents--automatic" v-if="!isNotified">
      <div class="AnnouncementStudents--automatic--info">
        <h1>{{ $t("ANNOUNCEMENT.FORM.STUDENTS_AUTOMATIC_ENROLLMENT") }}</h1>
        <p>
          {{ $t("ANNOUNCEMENT.FORM.STUDENTS_AUTOMATIC_ENROLLMENT_INFO") }}
        </p>
      </div>

      <div
        v-if="uploadingFiles"
        class="d-flex align-items-center justify-content-center"
      >
        <spinner />
      </div>
      <div class="AnnouncementStudents--automatic--actions" v-else>
        <a
          :href="`assets_announcement/template_xlsx/${enrollmentTemplateXls}`"
          class="btn btn-primary"
          download
        >
          <i class="fa fa-download"></i> {{ $t("DOWNLOAD_TEMPLATE") }}
        </a>

        <input
          type="file"
          accept=".xls, .xlsx"
          @change="handleFileUpload"
          ref="fileInput"
          style="display: none"
        />

        <button type="button" class="btn btn-primary" @click="openFileInput">
          <i class="fa fa-upload"></i> {{ $t("UPLOAD_TEMPLATE") }}
        </button>
      </div>
    </div>
    <div
      class="AnnouncementStudents--manual"
      v-if="course && startAt && finishAt && announcement.id"
    >
      <h1>{{ $t("ANNOUNCEMENT.FORM.STUDENTS_MANUAL_ENROLLMENT") }}</h1>
      <user-filter
        v-model="students"
        :realtime="false"
        :url-source="urlAvailableUsers"
        source-property="users"
        :group="true"
        :users-per-group="usersPerGroup"
        :disabled="isNotified"
        prefix="ANNOUNCEMENT"
        :use-global-events="true"
        :paginated="true"
      />
    </div>
    <div class="AnnouncementStudents--modal">
      <button
        type="button"
        id="open--AnnouncementStudents--modal--confirmation"
        data-bs-toggle="modal"
        data-bs-target="#AnnouncementStudents--modal--confirmation"
      />
      <BaseModal
        identifier="AnnouncementStudents--modal--confirmation"
        title="Grupos"
        modal-icon="fa-users"
      >
        <template>
          <div class="AnnouncementStudents--modal--confirmation--content">
            <div class="w-100 content">
              <h4>
                Se han seleccionado {{ numberOfStudents }} personas. El máximo
                por grupo permitido es de {{ usersPerGroup }}
              </h4>
              <h4 class="mt-3">Se van a crear {{ students.length }} grupos:</h4>
              <h4 v-for="(g, index) in students" :key="`confirmation-${g.id}`">
                <b>Grupo {{ index + 1 }}</b
                >, de {{ g.data.length }} personas.
              </h4>
              <h4 class="mt-3">¿Desea continuar?</h4>
            </div>
            <div class="w-100 footer">
              <button
                type="button"
                class="btn btn-danger"
                @click="closeConfirmationModal()"
              >
                Atrás
              </button>
              <button type="button" class="btn btn-primary" @click="confirm()">
                Confirmar
              </button>
            </div>
          </div>
        </template>
      </BaseModal>
    </div>
  </div>
</template>

<style scoped lang="scss">
.AnnouncementStudents {
  .AnnouncementStudents--modal {
    :deep(.modal-dialog) {
      max-width: 550px !important;
    }

    #open--AnnouncementStudents--modal--confirmation {
      display: none;
    }
    .AnnouncementStudents--modal--confirmation--content {
      padding: 1rem 3rem;
      display: flex;
      flex-flow: column;
      gap: 1rem;

      .icon {
        color: var(--color-tertiary);
        font-size: 3rem;
        margin-left: auto;
        margin-right: auto;
      }

      .content {
        text-align: center;
      }

      .footer {
        display: flex;
        flex-flow: row nowrap;
        justify-content: center;
        gap: 2rem;

        button {
          width: 100px;
        }
      }
    }
  }

  h1 {
    font-weight: bold;
    font-size: 20px;
    color: var(--color-neutral-darkest);
  }

  &--automatic {
    margin: 0 -1rem;
    background-color: #d8eef8;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    padding: 1rem;

    &--info {
      p {
        text-align: left;
        font-size: 17px;
        color: var(--color-neutral-darkest);
      }
    }

    &--actions {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 1rem;
      align-items: flex-start;
      justify-content: flex-end;

      button {
        &:first-child {
          grid-column-start: 2;
        }
        width: 100%;
      }
    }
  }

  &--manual {
    padding: 2rem 0;

    h2 {
      margin-top: 1rem;
      font-size: 16px;
      color: var(--color-neutral-darkest);
      i {
        margin-right: 1rem;
      }

      .badge {
        margin-left: 1rem;
        &.bg-secondary {
          background-color: #cad4df !important;
        }
      }
    }

    .UserFilter {
      margin-top: 2rem;
    }
  }
}
</style>
