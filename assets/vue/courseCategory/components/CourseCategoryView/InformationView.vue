<script>
import {get} from "vuex-pathify";
import ButtonWithDescription from "../../../common/components/ButtonWithDescription.vue";

export default {
  name: "InformationView",
  components: {ButtonWithDescription},
  data() {
    return {
      activeTypeCourse: {}
    };
  },
  computed: {
    internTypeCourses: get("configModule/config@intern"),
    externTypeCourses: get("configModule/config@extern"),
  }
}
</script>

<template>
  <div class="InformationView">
    <div class="InformationView--intern TypeCourses p-3">
      <h4>Tipo de cursos que se muestra en esta seccion</h4>
      <div class="TypeCourses--types">
        <button-with-description
            v-for="type in internTypeCourses" :key="type.id"
            :name="`intern-type-course-btn-${type.id}`"
            :title="type.name"
            description="Info aqui"
            :icon="type.icon"
            v-model="activeTypeCourse[type.id]"
        ></button-with-description>
      </div>
    </div>
    <div class="InformationView--extern TypeCourses p-3 mt-3">
      <h4 class="text-center">Formacion externa</h4>
      <div class="TypeCourses--types">
        <button-with-description
            v-for="type in externTypeCourses" :key="type.id"
            :name="`intern-type-course-btn-${type.id}`"
            :title="type.name"
            description="Info aqui"
            :icon="type.icon"
            v-model="activeTypeCourse[type.id]"
        ></button-with-description>
      </div>
    </div>

  </div>
</template>

<style scoped lang="scss">
.InformationView {
  display: grid;
  width: 100%;

  &--intern, &--extern {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr;
  }

  &--intern {
    & > h4 {
      border-bottom: 2px #808080 solid;
    }
  }

  &--extern {
    background-color: #D9F0FA;
    & > h4 {
      color: var(--color-primary);
    }
  }
}
.TypeCourses {
  & > h4 {
    width: 100%;
    text-align: left;
    font-size: 16px;
  }

  &--types {
    margin-top: 1rem;
    @media #{min-medium-screen()} {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
    }
  }
}
</style>
