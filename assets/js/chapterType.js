import $ from "jquery";
import axios from "axios";

$(function () {
    let chapterTypeArray = [];
    const pageEdit = $(".ea-edit");
    if(pageEdit.length > 0) {
        $(".chapter-type-selector-filters").hide();
        $(".chapter-type-list").hide()
    }

    function getChapterTypes (){
        axios.get('/admin/chapter/chaptertypes').then( r => {
            const { data, error } = r.data
            chapterTypeArray = data;
            const inputCheckRedirect = $("#Chapter_stateRedirect");
            $.each(chapterTypeArray, (index, item) => {
                $(`.content_chapter_${item}`).hide();
                $(`#change-state-${item}`).on('click', function (){
                    inputCheckRedirect.prop('checked', true)
                });
            });
        })
    }

    //getChapterTypes();

    /**
     * Verify that the required elements has been completed or has been selected
     */

    const currentFormId = $('.action-saveAndReturn').attr('form');
    const form = $('#' + currentFormId);
    const submitButton = $('.action-saveAndReturn');
    var withError = false;

    //Mutation observer
    var observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.attributeName === 'disabled') {
                if (withError) {
                    $(mutation.target).removeAttr('disabled');
                }
            }
        });
    });

    let elements = document.getElementsByName('ea[newForm][btn]');
    const tElements = elements.length
    for (let i = 0; i < tElements; i++) {
        observer.observe(elements[i], { attributes: true });
    }

    function scrollToStart() {
        const formId = $('.action-saveAndReturn').attr('form');
        document.getElementById(formId).scrollIntoView();
    }

    submitButton.click(e => {
        let $selected = $('.chapter-type-group input:checked').first();
        let chapterTypeSelector = $('.chapter-type-selector');
        if ($('.form-group').hasClass('has-error')) {
            scrollToStart();
        }
        console.log($selected);
        if($selected.length < 1){
            chapterTypeSelector.addClass('has-error-select');
        }
    });

    form.on('submit', e => {
        let selectedSeason = $('#Chapter_season').val().trim();
        if (selectedSeason.length < 1) {
            withError = true;
            scrollToStart();
            e.preventDefault();
            return;
        }

        let value = CKEDITOR.instances['Chapter_description'].document.getBody().getChild(0).getText().trim();
        if (!value || value.length < 1) {
            withError = true;
            scrollToStart();
            e.preventDefault();
            return;
        }
        let selected = $('.chapter-type-group input:checked').first();


        if (selected.length < 1) {
            console.log("value");
            withError = true;
            alert('Debe seleccionar el tipo de capitulo')
            e.preventDefault();
        }
    })


    const inputCheckRedirect = $('#Chapter_stateRedirect');
    $(document).on('click', '.chapter-type-group', function () {
        inputCheckRedirect.prop('checked', true);
    });


    const chapterTypeIds = [1, 8, 9];
    //const chapterTypeIds = ['SCORM', 'PDF', 'VIDEO'];
    const idTypeChapterDefault =  typeof typeChapterId  !== 'undefined' ? typeChapterId : '';
    if((modalChapter != '' && modalChapter != null) || chapterTypeIds.includes(idTypeChapterDefault)){
        $("html, body").animate({ scrollTop: $(document).height() });
    }

    if(modalChapter && typeChapterId !== '16' ){
        $('[data-bs-toggle="modal"]').trigger('click');
    }


    $('.chapter-type-selector-filters button').on('click', function () {
        const filter = $(this).data('filter');
        $('.chapter-type-selector-filters button').removeClass('selected');
        $(this).addClass('selected');
        if (filter) {
            $('.chapter-type-group').hide();
            $('.chapter-type-group[data-type=' + filter + ']').show();
        } else {
            $('.chapter-type-group').show();
        }
    });

    $('.chapter-type-group input').on('click', function () {
        const value = $(this).val();
        $('.content_chapter_btn').hide();
        $(`.content_chapter_${value}`).show();
        $('.selected-chapter-type-info').removeClass('hidden');
        $('.selected-chapter-type-name').text($(this).data('name'));
        $('.selected-chapter-type-description').html($(this).data('description'));
    
        if (!$(this).data('video')) {
            $('.selected-chapter-type-video').hide();
            $('.selected-chapter-type-video iframe').attr('src', '');
            $('.selected-chapter-type-thumbnail img').attr('src', $(this).data('thumbnail'));
            $('.selected-chapter-type-thumbnail').show();
        } else {
            $('.selected-chapter-type-thumbnail').hide();
            $('.selected-chapter-type-thumbnail img').attr('src', '');
            $('.selected-chapter-type-video iframe').attr('src', $(this).data('video'));
            $('.selected-chapter-type-video').show();
        }
    });

    let $selected = $('.chapter-type-group input:checked').first();

    if ($selected.length) {
        let chapterTypeSelector = $('.chapter-type-selector');
        chapterTypeSelector.removeClass('has-error-select');
        $('.selected-chapter-type-name').text($selected.data('name'));
        $('.selected-chapter-type-description').html($selected.data('description'));

        if($selected.data('video')) {
            $('.selected-chapter-type-video iframe').attr('src', $selected.data('video'));
        }
        else{
            $('.selected-chapter-type-thumbnail img').attr('src', $selected.data('thumbnail'));
        }

        const valueSelected =  $selected.val();
        $(`.content_chapter_${valueSelected}`).show();
    }

    if (document.getElementsByClassName('preview-image'))
        $('.ea-vich-image').append('<div id="preview-image" class="preview-image"></div>');

    const inputFile = $('.chapter-image-selector').find('input[type=file]');
    let editPreview =  $('.chapter-image-selector').find('.ea-lightbox-thumbnail');
    const previewImage = $('#preview-image');
    previewImage.on('click', function () {
        inputFile.click();
    });

    inputFile.on('change', function (event) {
        const files = inputFile.prop('files');
        if (files.length > 0) {
            let reader = new FileReader();
            reader.onload = e => {
                //console.log("edit preview", reader.result);
                 if (editPreview != null) {

                   editPreview.css('display', 'none');
                   previewImage.css('background-image', 'url(' + reader.result + ')');
                   previewImage.addClass('with-image')
                }

                previewImage.css('background-image', 'url(' + reader.result + ')');
                previewImage.addClass('with-image');

            };
            reader.readAsDataURL(files[0]);
        }
    });
});
