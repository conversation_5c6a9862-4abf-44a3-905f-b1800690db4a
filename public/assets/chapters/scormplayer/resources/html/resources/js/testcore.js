	var QUESTION_TYPE_CHOICE = "choice";
var QUESTION_TYPE_TF = "true-false";
var QUESTION_TYPE_NUMERIC = "numeric";

	logTxt('go testcore','');
	
	class Test	{
				constructor(questions) 
					{
					this.Questions = questions;
					}	
				AddQuestion(question)
					{
					logTxt('AddQuestion añadimos:',question);
					this.Questions[this.Questions.length] = question;	
					}
				}
	
	
function Question(id, text, type, answers, correctAnswer, objectiveId){
		logTxt('go Question',id);
this.Id = id;
this.Text = text;
this.Type = type;
this.Answers = answers;
this.CorrectAnswer = correctAnswer;
this.ObjectiveId = objectiveId;
}

/*function Test(questions){
		logTxt('go Test');
this.Questions = questions;
}
Test.prototype.AddQuestion = function(question)
{
		logTxt('go Test AddQuestion');
this.Questions[this.Questions.length] = question;
}*/

		 function CheckNumeric(obj){
		var userText = new String(obj.value);
		var numbersRegEx = /[^0-9]/g;
		if (userText.search(numbersRegEx) >= 0){
				alert("Please enter only numeric values.");
				obj.value = userText.replace(numbersRegEx, "");
		}
	 }
	 function SubmitAnswers(){
		var correctCount = 0;
		var totalQuestions = test.Questions.length;
		
		var resultsSummary = "";
		
		for (var i in test.Questions){
				var question = test.Questions[i];
				
				var wasCorrect = false;
				var correctAnswer = null;
				var learnerResponse = "";
				
				switch (question.Type){
					 case QUESTION_TYPE_CHOICE:

						for (var answerIndex = 0; answerIndex < question.Answers.length; answerIndex++){
								
								if (question.CorrectAnswer == question.Answers[answerIndex]){
									 correctAnswer = answerIndex;
								}
								if (document.getElementById("question_" + question.Id + "_" + answerIndex).checked == true){
									 learnerResponse = answerIndex;
								}
						}

					 break;
					 
					 case QUESTION_TYPE_TF:
						
						if (document.getElementById("question_" + question.Id + "_True").checked == true){
								learnerResponse = "true";
						}
						if (document.getElementById("question_" + question.Id + "_False").checked == true){
							learnerResponse = "false";
						} 
							
						if (question.CorrectAnswer == true){
								correctAnswer = "true";
						}
						else{
								correctAnswer = "false"; 
						}
					 break;
					 
					 case QUESTION_TYPE_NUMERIC:
						correctAnswer = question.CorrectAnswer;
						learnerResponse = document.getElementById("question_" + question.Id + "_Text").value;
					 break;
					 
					 default:
						alert("invalid question type detected");
					 break;
				}
				
				wasCorrect = (correctAnswer == learnerResponse);
				if (wasCorrect) {correctCount++;}
				
				if (parent.RecordQuestion){
					 parent.RecordQuestion(test.Questions[i].Id, 
													 test.Questions[i].Text, 
													 test.Questions[i].Type, 
													 learnerResponse, 
													 correctAnswer, 
													 wasCorrect, 
													 test.Questions[i].ObjectiveId);
				}
				
				resultsSummary += "<div class='questionResult'><h3>Question " + i + "</h3>";
				if (wasCorrect) {
					 resultsSummary += "<em>Correct</em><br>"
				}
				else{
					 resultsSummary += "<em>Incorrect</em><br>"
					 resultsSummary += "Your answer: " + learnerResponse + "<br>"
					 resultsSummary += "Correct answer: " + correctAnswer + "<br>"
				}
				resultsSummary += "</div>";
		}
		var score = Math.round(correctCount * 100 / totalQuestions);
		resultsSummary = "<h3>Score: " + score + "</h3>" + resultsSummary;
		document.getElementById("test").innerHTML = resultsSummary;
		
		if (parent.RecordTest){
				parent.RecordTest(score);
		}
	 }

	function RenderTest(test)
	{
	var newTest = '';	
	logTxt('RenderTest:',test);
		  newTest += ("<div id='test'><form id='frmTest' action='#'>");
		  
		  for (var i in test.Questions){
				var question = test.Questions[i];
				
				newTest += ("<div id='question_" + question.Id + "' class='question'>");
				newTest += (question.Text);
				
				switch (question.Type){
					 case QUESTION_TYPE_CHOICE:
						  var ansIndex = 0;
						  for (var j in question.Answers){
								var answer = question.Answers[j];
								newTest += ("<div ");
								if (question.CorrectAnswer == answer) {newTest += ("class='correctAnswer'");} else{newTest += ("class='answer'");}
								newTest += ("><input type='radio' name='question_" + question.Id + "_choices' id='question_" + question.Id + "_" + ansIndex + "'/>" + answer + "</div>");
								ansIndex++;
						  }
					 break;
					 
					 case QUESTION_TYPE_TF:
						  
						  newTest += ("<div ");
						  if (question.CorrectAnswer == true) {newTest += ("class='correctAnswer'");}else{newTest += ("class='answer'");}
						  newTest += ("><input type='radio' name='question_" + question.Id + "_choices' id='question_" + question.Id + "_True'/>True</div>");
						  
						  newTest += ("<div ");
						  if (question.CorrectAnswer == false) {newTest += ("class='correctAnswer'");}else{newTest += ("class='answer'");}
						  newTest += ("><input type='radio' name='question_" + question.Id + "_choices' id='question_" + question.Id + "_False'/>False</div>");
					 break;
					 
					 case QUESTION_TYPE_NUMERIC:
						  newTest += ("<div class='correctAnswer'><input type='text' value='' id='question_" + question.Id + "_Text' onchange='CheckNumeric(this)'/> (");
						  newTest += (question.CorrectAnswer + ")</div>");
					 break;
					 
					 default:
						  alert("invalid question type detected");
					 break;
				}
				newTest += ("</div>");		//close out question div
		  }
		  newTest += ("<input type='button' value='Submit Answers' onclick='SubmitAnswers();' />");
		  newTest += ("</form></div>");		//close out test div
		  return newTest;
		  }
	
	function AddTagLine()
		{
		$('.pie').html('<div class="ftr"><p>Información en el pie de pagina: Esto es un test</p></div>');
		}
		
	function Start() 
		{
		logTxt('TC Start','');	
		}
		
	function Unload() 
		{
		logTxt('TC Unload','');	
		}

	function WriteToDebug() 
		{
		logTxt('TC WriteToDebug','');	
		}	

	function logTxt(texto, valor)
		{
		//console.log(texto.indexOf(' -> '));	
		if(typeof(texto) != 'object' )
			{
			texto = texto + ' -> ' + '<span class="sb">'+valor+'</span>';
			}
		else
			{
			texto = texto + ' -> ' + '<span class="sb">'+JSON.parse(valor)+'</span>';
			}
		$('#infolog').append(texto + "<br>");
		}